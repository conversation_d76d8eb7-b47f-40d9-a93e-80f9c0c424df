# Server Configuration
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
LOG_LEVEL=info

# API Keys
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# Google Cloud Configuration (for Vertex AI)
GOOGLE_CLOUD_PROJECT=your_project_id
GOOGLE_CLOUD_LOCATION=us-central1

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000

# Security
CORS_ORIGIN=*
HELMET_ENABLED=true

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# Model Configuration
DEFAULT_MODEL_PROVIDER=openai
DEFAULT_OPENAI_MODEL=gpt-4o
DEFAULT_GEMINI_MODEL=gemini-2.0-flash

# Timeout Configuration
REQUEST_TIMEOUT=30000
STREAM_TIMEOUT=60000

# Retry Configuration
MAX_RETRIES=3
RETRY_DELAY=1000

# Load Balancing
ENABLE_LOAD_BALANCING=true
FALLBACK_ENABLED=true

# Database (if needed for future extensions)
# DATABASE_URL=postgresql://user:password@localhost:5432/ai_proxy

# Redis (for caching and rate limiting)
# REDIS_URL=redis://localhost:6379
