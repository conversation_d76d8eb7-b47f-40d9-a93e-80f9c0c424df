{"name": "ai-proxy-service", "version": "1.0.0", "description": "High-performance AI proxy service with dual-mode responses and multi-provider support", "main": "dist/app.js", "scripts": {"build": "tsc && tsc-alias", "start": "node dist/app.js", "dev": "tsx watch src/app.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "docker:build": "docker build -t ai-proxy-service .", "docker:run": "docker run -p 3000:3000 ai-proxy-service", "health-check": "curl -f http://localhost:3000/health || exit 1"}, "keywords": ["ai", "proxy", "fastify", "openai", "gemini", "sse", "streaming", "typescript"], "author": "AI Proxy Service Team", "license": "MIT", "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/env": "^4.4.0", "@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^9.1.0", "@fastify/swagger": "^8.14.0", "@fastify/swagger-ui": "^2.1.0", "@fastify/under-pressure": "^8.3.0", "@google/genai": "^0.3.0", "axios": "^1.6.2", "dotenv": "^16.3.1", "fastify": "^4.28.1", "fastify-sse-v2": "^3.1.0", "joi": "^17.11.0", "lodash": "^4.17.21", "openai": "^4.68.0", "p-retry": "^6.2.0", "p-timeout": "^6.1.2", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/lodash": "^4.14.202", "@types/node": "^20.10.4", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "tsc-alias": "^1.8.16", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}}