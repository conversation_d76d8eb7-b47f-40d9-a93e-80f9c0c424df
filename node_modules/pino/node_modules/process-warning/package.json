{"name": "process-warning", "version": "5.0.0", "description": "A small utility for creating warnings and emitting them.", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:jest && npm run test:typescript", "test:jest": "jest jest.test.js", "test:unit": "c8 --100 node --test", "test:typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/process-warning.git"}, "keywords": ["fastify", "error", "warning", "utility", "plugin", "emit", "once"], "author": "<PERSON>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://james.sumners.info"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-warning/issues"}, "homepage": "https://github.com/fastify/fastify-warning#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"@fastify/pre-commit": "^2.1.0", "benchmark": "^2.1.4", "c8": "^10.1.3", "eslint": "^9.17.0", "jest": "^29.7.0", "neostandard": "^0.12.0", "tsd": "^0.31.0"}}