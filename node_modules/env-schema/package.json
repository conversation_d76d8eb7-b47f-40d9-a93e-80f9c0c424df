{"name": "env-schema", "version": "6.0.1", "description": "Validate your env variables using Ajv and dotenv", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:typescript", "test:unit": "c8 --100 node --test", "test:typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/env-schema.git"}, "keywords": ["ajv", "env", "schema", "json", "dotenv", "validate", "extract"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://maksim.dev"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/env-schema/issues"}, "homepage": "https://github.com/fastify/env-schema#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "dependencies": {"ajv": "^8.12.0", "dotenv": "^16.4.5", "dotenv-expand": "10.0.0"}, "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@sinclair/typebox": "^0.34.3", "ajv-formats": "^3.0.1", "c8": "^10.1.2", "eslint": "^9.17.0", "fluent-json-schema": "^5.0.0", "neostandard": "^0.12.0", "tsd": "^0.31.0"}, "pre-commit": ["lint", "test"]}