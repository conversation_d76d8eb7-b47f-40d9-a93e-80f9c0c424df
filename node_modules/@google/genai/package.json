{"name": "@google/genai", "version": "0.3.1", "description": "", "main": "dist/node/index.js", "module": "dist/web/index.mjs", "browser": "dist/web/index.mjs", "typings": "dist/genai.d.ts", "exports": {".": {"browser": {"types": "./dist/web/web.d.ts", "import": "./dist/web/index.mjs", "default": "./dist/web/index.mjs"}, "node": {"types": "./dist/node/node.d.ts", "require": "./dist/node/index.js", "default": "./dist/node/index.js"}, "types": "./dist/genai.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs", "default": "./dist/index.js"}, "./web": {"types": "./dist/web/web.d.ts", "import": "./dist/web/index.mjs", "default": "./dist/web/index.mjs"}, "./node": {"types": "./dist/node/node.d.ts", "require": "./dist/node/index.js", "default": "./dist/node/index.js"}}, "scripts": {"build": "rollup -c && api-extractor run --local --verbose && api-extractor run -c api-extractor.node.json --local --verbose&& api-extractor run -c api-extractor.web.json --local --verbose", "build-prod": "rollup -c && api-extractor run --verbose && api-extractor run -c api-extractor.node.json --verbose && api-extractor run -c api-extractor.web.json --verbose", "unit-test": "ts-node node_modules/jasmine/bin/jasmine test/unit/**/*_test.ts test/unit/*_test.ts", "system-test": "ts-node node_modules/jasmine/bin/jasmine test/system/**/*_test.ts", "docs": "typedoc && scripts/add_typedoc_headers.sh", "format": "prettier '**/*.ts' '**/*.mjs' '**/*.json' --write", "lint": "eslint '**/*.ts'", "lint-fix": "eslint --fix '**/*.ts'"}, "engines": {"node": ">=18.0.0"}, "files": ["dist/genai.d.ts", "dist/index.js", "dist/index.js.map", "dist/index.mjs", "dist/index.mjs.map", "dist/node/index.js", "dist/node/index.js.map", "dist/node/index.mjs", "dist/node/index.mjs.map", "dist/node/node.d.ts", "dist/web/index.js", "dist/web/index.js.map", "dist/web/index.mjs", "dist/web/index.mjs.map", "dist/web/web.d.ts", "node/package.json", "web/package.json"], "devDependencies": {"@eslint/js": "9.20.0", "@microsoft/api-extractor": "^7.50.1", "@rollup/plugin-json": "^6.1.0", "@types/jasmine": "^5.1.2", "@types/node": "^20.9.0", "@types/unist": "^3.0.3", "@types/ws": "^8.5.14", "eslint": "8.57.0", "gts": "^5.2.0", "jasmine": "^5.5.0", "jasmine-reporters": "^2.4.0", "nyc": "^17.1.0", "prettier": "3.3.3", "prettier-plugin-organize-imports": "^4.1.0", "rollup-plugin-typescript2": "^0.36.0", "ts-node": "^10.9.2", "tslib": "^2.8.1", "typedoc": "^0.27.0", "typescript": "~5.2.0", "typescript-eslint": "8.24.1"}, "dependencies": {"google-auth-library": "^9.14.2", "ws": "^8.18.0"}, "repository": {"type": "git", "url": "https://github.com/googleapis/js-genai.git"}, "bugs": {"url": "https://github.com/googleapis/js-genai/issues"}, "homepage": "https://github.com/googleapis/js-genai#readme", "author": "", "license": "Apache-2.0"}