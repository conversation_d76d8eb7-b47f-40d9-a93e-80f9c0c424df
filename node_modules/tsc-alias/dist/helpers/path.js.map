{"version": 3, "file": "path.js", "sourceRoot": "", "sources": ["../../src/helpers/path.ts"], "names": [], "mappings": ";;;AAOA,gDAAiD;AACjD,mCAA8B;AAC9B,+BAAoD;AASpD,SAAS,yBAAyB,CAChC,MAAc,EACd,UAAkB;IAElB,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC/C,MAAM,IAAI,GAAG,IAAA,aAAI,EACf;QACE,GAAG,WAAW,OAAO,UAAU,EAAE;QACjC,IAAI,WAAW,OAAO,UAAU,OAAO,UAAU,EAAE;QACnD,IAAI,WAAW,kBAAkB;KAClC,EACD;QACE,GAAG,EAAE,IAAI;QACT,eAAe,EAAE,IAAI;KACtB,CACF,CAAC;IAGF,OAAO,IAAI,CAAC,MAAM,CAChB,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CACb,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAC/D,IAAI,CAAC,CAAC,CAAC,CACR,CAAC;AACJ,CAAC;AAMD,SAAgB,0BAA0B,CAAC,MAAsB;IAC/D,MAAM,CAAC,kBAAkB,GAAG,yBAAyB,CACnD,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,uBAAuB,CAC/B,CAAC;IAGF,IAAI,MAAM,CAAC,kBAAkB,EAAE;QAC7B,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,MAAM,aAAa,GAAG,IAAA,eAAQ,EAAC,MAAM,CAAC,kBAAkB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAC1E,MAAM,iBAAiB,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClE,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC;QAC9C,MAAM,qBAAqB,GAAG,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEnE,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,OAAO,CAAC,IAAI,YAAY,EAAE;YACxB,YAAY,CAAC,OAAO,CAClB,qBAAqB,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,CAAC,CACxD,CAAC;YACF,CAAC,EAAE,CAAC;SACL;QACD,MAAM,CAAC,uBAAuB,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACzD;AACH,CAAC;AAxBD,gEAwBC;AAOD,SAAgB,mBAAmB,CAAC,MAAsB;IACxD,OAAO,CAAC,IAAY,EAAE,EAAE;QACtB,MAAM,SAAS,GAAG,EAAE,IAAI,EAAe,CAAC;QAGxC,IAAI,IAAA,gBAAS,EAAC,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC5C,MAAM,YAAY,GAAG,aAAa,CAChC,IAAA,gBAAS,EACP,GAAG,MAAM,CAAC,MAAM,GAAG;gBACjB,GACE,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,uBAAuB;oBACrD,CAAC,CAAC,MAAM,CAAC,uBAAuB;oBAChC,CAAC,CAAC,EACN,IAAI,MAAM,CAAC,OAAO,EAAE,CACvB,CACF,CAAC;YAEF,MAAM,gBAAgB,GAAG,aAAa,CACpC,IAAA,gBAAS,EAAC,GAAG,YAAY,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC,CAC/C,CAAC;YAEF,IAAI,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,EAAE;gBAC1D,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;gBAC1B,SAAS,CAAC,QAAQ,GAAG,YAAY,CAAC;aACnC;iBAAM;gBACL,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;gBACzB,SAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC;aACvC;YAED,OAAO,SAAS,CAAC;SAClB;QAQD,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,EAAE;YACjD,SAAS,CAAC,QAAQ,GAAG,IAAA,cAAO,EAAC,MAAM,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;YAC7D,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;YAC1B,OAAO,SAAS,CAAC;SAClB;QAGD,IAAI,MAAM,CAAC,cAAc,EAAE;YACzB,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;YAC1B,SAAS,CAAC,QAAQ,GAAG,aAAa,CAChC,IAAA,gBAAS,EACP,GAAG,MAAM,CAAC,MAAM,GAAG;gBACjB,GAAG,MAAM,CAAC,uBAAuB,IAAI,MAAM,CAAC,OAAO,EAAE,CACxD,CACF,CAAC;YACF,OAAO,SAAS,CAAC;SAClB;QAED,SAAS,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC;QACnC,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;QAC1B,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC;AACJ,CAAC;AA5DD,kDA4DC", "sourcesContent": ["/**\n * @file\n *\n * This file has all helperfunctions related to path resolution.\n */\n\n/** */\nimport normalizePath = require('normalize-path');\nimport { sync } from 'globby';\nimport { normalize, relative, resolve } from 'path';\nimport { AliasPath, IProjectConfig } from '../interfaces';\n\n/**\n * getProjectDirPathInOutDir finds the configDirInOutPath.\n * @param {string} outDir outDir loaded from tsconfig.\n * @param {string} projectDir  projectDir loaded from tsconfig.\n * @returns {string} the configDirInOutPath.\n */\nfunction getProjectDirPathInOutDir(\n  outDir: string,\n  projectDir: string\n): string | undefined {\n  const posixOutput = outDir.replace(/\\\\/g, '/');\n  const dirs = sync(\n    [\n      `${posixOutput}/**/${projectDir}`,\n      `!${posixOutput}/**/${projectDir}/**/${projectDir}`,\n      `!${posixOutput}/**/node_modules`\n    ],\n    {\n      dot: true,\n      onlyDirectories: true\n    }\n  );\n\n  // Find the longest path\n  return dirs.reduce(\n    (prev, curr) =>\n      prev.split('/').length > curr.split('/').length ? prev : curr,\n    dirs[0]\n  );\n}\n\n/**\n * relativeOutPathToConfigDir\n * Finds relative path access of configDir in outPath\n */\nexport function relativeOutPathToConfigDir(config: IProjectConfig) {\n  config.configDirInOutPath = getProjectDirPathInOutDir(\n    config.outPath,\n    config.confDirParentFolderName\n  );\n\n  // Find relative path access of configDir in outPath\n  if (config.configDirInOutPath) {\n    config.hasExtraModule = true;\n    const stepsbackPath = relative(config.configDirInOutPath, config.outPath);\n    const splitStepBackPath = normalizePath(stepsbackPath).split('/');\n    const nbOfStepBack = splitStepBackPath.length;\n    const splitConfDirInOutPath = config.configDirInOutPath.split('/');\n\n    let i = 1;\n    const splitRelPath: string[] = [];\n    while (i <= nbOfStepBack) {\n      splitRelPath.unshift(\n        splitConfDirInOutPath[splitConfDirInOutPath.length - i]\n      );\n      i++;\n    }\n    config.relConfDirPathInOutPath = splitRelPath.join('/');\n  }\n}\n\n/**\n * findBasePathOfAlias finds a basepath for every AliasPath.\n * And checks if isExtra should be true or false.\n * @param {IProjectConfig} config config object with all config values.\n */\nexport function findBasePathOfAlias(config: IProjectConfig) {\n  return (path: string) => {\n    const aliasPath = { path } as AliasPath;\n\n    // If it's an alias that references a file outside the baseUrl\n    if (normalize(aliasPath.path).includes('..')) {\n      const tempBasePath = normalizePath(\n        normalize(\n          `${config.outDir}/` +\n            `${\n              config.hasExtraModule && config.relConfDirPathInOutPath\n                ? config.relConfDirPathInOutPath\n                : ''\n            }/${config.baseUrl}`\n        )\n      );\n\n      const absoluteBasePath = normalizePath(\n        normalize(`${tempBasePath}/${aliasPath.path}`)\n      );\n\n      if (config.pathCache.existsResolvedAlias(absoluteBasePath)) {\n        aliasPath.isExtra = false;\n        aliasPath.basePath = tempBasePath;\n      } else {\n        aliasPath.isExtra = true;\n        aliasPath.basePath = absoluteBasePath;\n      }\n\n      return aliasPath;\n    }\n\n    /**\n     * If the alias refers to a file in the node_modules folder\n     * located at the same level of baseUrl.\n     * Because typescript will not include the node_modules\n     * folder in the output folder (outDir).\n     */\n    if (aliasPath.path.match(/^(\\.\\/|)node_modules/g)) {\n      aliasPath.basePath = resolve(config.baseUrl, 'node_modules');\n      aliasPath.isExtra = false;\n      return aliasPath;\n    }\n\n    // If the project references another external project\n    if (config.hasExtraModule) {\n      aliasPath.isExtra = false;\n      aliasPath.basePath = normalizePath(\n        normalize(\n          `${config.outDir}/` +\n            `${config.relConfDirPathInOutPath}/${config.baseUrl}`\n        )\n      );\n      return aliasPath;\n    }\n\n    aliasPath.basePath = config.outDir;\n    aliasPath.isExtra = false;\n    return aliasPath;\n  };\n}\n"]}