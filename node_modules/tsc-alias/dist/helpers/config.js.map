{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/helpers/config.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAOA,2BAA2C;AAC3C,+CAAmE;AACnE,iCAAkC;AAClC,+BAAoE;AASpE,oCAAuD;AACvD,2CAA8C;AAC9C,gDAAiD;AAOjD,SAAsB,aAAa,CACjC,OAAoC;;;QAEpC,MAAM,MAAM,GAAG,MAAA,OAAO,CAAC,MAAM,mCAAI,IAAI,cAAM,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;QAE5E,MAAM,UAAU,GAAG,CAAC,OAAO,CAAC,UAAU;YACpC,CAAC,CAAC,IAAA,cAAO,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,eAAe,CAAC;YACzC,CAAC,CAAC,CAAC,IAAA,iBAAU,EAAC,OAAO,CAAC,UAAU,CAAC;gBACjC,CAAC,CAAC,IAAA,cAAO,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,UAAU,CAAC;gBAC5C,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;QAEvB,MAAM,CAAC,MAAM,CAAC,IAAA,eAAU,EAAC,UAAU,CAAC,EAAE,wBAAwB,UAAU,EAAE,CAAC,CAAC;QAE5E,MAAM,EACJ,OAAO,GAAG,EAAE,EACZ,MAAM,EACN,cAAc,EACd,KAAK,EACL,SAAS,EACT,gBAAgB,EAChB,OAAO,EACP,cAAc,EACf,GAAG,IAAA,kBAAU,EAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAEnC,IAAI,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,SAAS,EAAE;YACtC,cAAc,CAAC,SAAS,GAAG,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC;SAC7D;QACD,IAAI,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,cAAc,0CAAE,WAAW,EAAE;YACxC,cAAc,CAAC,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC;SACjE;QAED,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;QAEzB,IAAI,OAAO,CAAC,gBAAgB,IAAI,gBAAgB,EAAE;YAChD,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAC3C,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;SACjC;QAED,MAAM,OAAO,GAAG,MAAA,OAAO,CAAC,MAAM,mCAAI,MAAM,CAAC;QACzC,IAAI,cAAc,IAAI,OAAO,KAAK,cAAc,EAAE;YAChD,MAAA,OAAO,CAAC,cAAc,oCAAtB,OAAO,CAAC,cAAc,GAAK,cAAc,EAAC;SAC3C;QAED,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,mCAAmC,CAAC,CAAC;QAE5D,MAAM,SAAS,GAAW,aAAa,CAAC,IAAA,cAAO,EAAC,UAAU,CAAC,CAAC,CAAC;QAG7D,MAAM,aAAa,GAAmB;YACpC,UAAU,EAAE,UAAU;YACtB,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,OAAO;YACf,SAAS,EAAE,SAAS;YACpB,OAAO,EAAE,OAAO;YAChB,uBAAuB,EAAE,IAAA,eAAQ,EAAC,SAAS,CAAC;YAC5C,cAAc,EAAE,KAAK;YACrB,kBAAkB,EAAE,IAAI;YACxB,uBAAuB,EAAE,IAAI;YAC7B,SAAS,EAAE,IAAI,iBAAS,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,WAAW,CAAC;YACrE,SAAS,EACP,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,SAAS,KAAI,qCAAqC;SACrE,CAAC;QACF,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,aAAa,CAAC,CAAC;QAEtD,MAAM,MAAM,mCACP,aAAa,KAChB,MAAM,EAAE,MAAM,EACd,SAAS,EACP,MAAA,OAAO,CAAC,SAAS,mCAAI,gBAAQ,CAAC,cAAc,CAAC,aAAa,EAAE,KAAK,CAAC,EACpE,SAAS,EAAE,EAAE,GACd,CAAC;QACF,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QAG5C,MAAM,IAAA,2BAAe,EAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAC5D,OAAO,MAAM,CAAC;;CACf;AA5ED,sCA4EC;AAED,SAAS,2BAA2B,CAAC,IAAY,EAAE,SAAiB;IAClE,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;AACrD,CAAC;AAQM,MAAM,UAAU,GAAG,CACxB,IAAY,EACZ,MAAe,EACf,gBAA+B,IAAI,EACxB,EAAE;;IACb,IAAI,CAAC,IAAA,eAAU,EAAC,IAAI,CAAC,EAAE;QACrB,MAAM,CAAC,KAAK,CAAC,QAAQ,IAAI,YAAY,EAAE,IAAI,CAAC,CAAC;KAC9C;IACD,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;IAE3C,MAAM,QAAQ,GAAG,IAAA,4BAAa,EAAC,IAAI,CAAC,CAAC;IACrC,MAAM,YAAY,GAAG,YAAI,CAAC,KAAK,CAAuB,IAAI,EAAE,IAAI,CAAC,CAAC;IAClE,MAAM,EACJ,eAAe,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG;QAC5D,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE,SAAS;QACjB,cAAc,EAAE,SAAS;QACzB,KAAK,EAAE,SAAS;KACjB,EACD,WAAW,EAAE,cAAc,EAC5B,GAAG,QAAmE,CAAC;IAExE,MAAM,SAAS,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,CAAC;IAChC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IACrC,MAAM,MAAM,GAAc,EAAE,CAAC;IAE7B,IAAI,OAAO,EAAE;QACX,IAAI,aAAa,KAAK,IAAI,EAAE;YAC1B,MAAM,CAAC,OAAO,GAAG,2BAA2B,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;SACtE;aAAM;YACL,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;SAC1B;KACF;IACD,IAAI,MAAM,KAAI,MAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,eAAe,0CAAE,MAAM,CAAA,EAAE;QACnD,IAAI,cAAc,GAAG,MAAM,KAAI,MAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,eAAe,0CAAE,MAAM,CAAA,CAAC;QACrE,IAAI,aAAa,KAAK,IAAI,EAAE;YAC1B,cAAc,GAAG,2BAA2B,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;SACrE;QACD,MAAM,CAAC,MAAM,GAAG,IAAA,iBAAU,EAAC,cAAc,CAAC;YACxC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,IAAA,WAAI,EAAC,SAAS,EAAE,cAAc,CAAC,CAAC;KACrC;IACD,IAAI,KAAK,EAAE;QACT,IAAI,aAAa,KAAK,IAAI,EAAE;YAC1B,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;gBACvB,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CACnC,2BAA2B,CAAC,IAAI,EAAE,aAAa,CAAC,CACjD,CAAC;aACH;SACF;QACD,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;IACD,IAAI,cAAc,EAAE;QAClB,IAAI,sBAAsB,GAAG,cAAc,CAAC;QAC5C,IAAI,aAAa,KAAK,IAAI,EAAE;YAC1B,sBAAsB,GAAG,2BAA2B,CAClD,cAAc,EACd,aAAa,CACd,CAAC;SACH;QACD,MAAM,CAAC,cAAc,GAAG,IAAA,iBAAU,EAAC,sBAAsB,CAAC;YACxD,CAAC,CAAC,sBAAsB;YACxB,CAAC,CAAC,IAAA,WAAI,EAAC,SAAS,EAAE,sBAAsB,CAAC,CAAC;KAC7C;IACD,IAAI,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,SAAS,EAAE;QAC7B,MAAM,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;KAC7C;IACD,IAAI,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,gBAAgB,EAAE;QACpC,MAAM,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;KAC3D;IACD,IAAI,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,OAAO,EAAE;QAC3B,MAAM,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;KACzC;IACD,MAAM,CAAC,cAAc,GAAG,MAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,cAAc,mCAAI,EAAE,CAAC;IAE7D,MAAM,YAAY,GAAG,MAAA,MAAA,MAAM,CAAC,SAAS,0CAAE,YAAY,0CAAE,IAAI,CAAC;IAE1D,IAAI,YAAY,EAAE;QAChB,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,YAAY,CAAC,CAAC;KACpE;IAED,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,MAAM,CAAC,CAAC;IAEnD,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AApFW,QAAA,UAAU,cAoFrB;AAQF,SAAgB,8BAA8B,CAC5C,GAAsB,EACtB,IAAY;IAEZ,IAAI,CAAC,GAAG;QAAE,OAAO,EAAE,CAAC;IACpB,MAAM,SAAS,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,CAAC;IAChC,MAAM,QAAQ,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAC5D,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC;QACf,CAAC,CAAC,IAAA,WAAI,EAAC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;QACxD,CAAC,CAAC,0BAA0B,CAAC,CAAC,EAAE,IAAI,CAAC,CACxC,CAAC;IACF,OAAO,QAAQ,CAAC;AAClB,CAAC;AAZD,wEAYC;AAQD,SAAgB,0BAA0B,CAAC,GAAW,EAAE,IAAY;IAClE,MAAM,WAAW,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,CAAC;IAClC,MAAM,YAAY,GAAa,WAAG,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC;IACrE,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,WAAI,EAAC,WAAW,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAGvE,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;QACpC,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAEzB,IAAI,IAAA,eAAU,EAAC,UAAU,CAAC,EAAE;gBAC1B,OAAO,UAAU,CAAC;aACnB;iBAAM;gBACL,SAAS;aACV;SACF;QACD,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI;YACF,MAAM,KAAK,GAAG,IAAA,cAAS,EAAC,UAAU,CAAC,CAAC;YACpC,WAAW,GAAG,KAAK,CAAC,WAAW,EAAE,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;SAC7D;QAAC,OAAO,GAAG,EAAE,GAAE;QAChB,IAAI,WAAW,EAAE;YACf,OAAO,IAAA,WAAI,EAAC,UAAU,EAAE,eAAe,CAAC,CAAC;SAC1C;aAAM;YAEL,IAAI,IAAA,eAAU,EAAC,GAAG,UAAU,OAAO,CAAC,EAAE;gBACpC,OAAO,GAAG,UAAU,OAAO,CAAC;aAC7B;SACF;KACF;AACH,CAAC;AA7BD,gEA6BC", "sourcesContent": ["/**\n * @file\n *\n * This file has all helperfunctions related to configuration.\n */\n\n/** */\nimport { existsSync, lstatSync } from 'fs';\nimport { parseTsconfig, TsConfigJsonResolved } from 'get-tsconfig';\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from 'mylas';\nimport { basename, dirname, isAbsolute, join, resolve } from 'path';\nimport {\n  IConfig,\n  IOutput,\n  IProjectConfig,\n  ITSCAliasConfig,\n  ITSConfig,\n  ReplaceTscAliasPathsOptions\n} from '../interfaces';\nimport { Output, PathCache, TrieNode } from '../utils';\nimport { importReplacers } from './replacers';\nimport normalizePath = require('normalize-path');\n\n/**\n * prepareConfig prepares a IConfig object for tsc-alias to be used.\n * @param {ReplaceTscAliasPathsOptions} options options that are used to prepare a config object.\n * @returns {Promise<IConfig>} a promise of a IConfig object.\n */\nexport async function prepareConfig(\n  options: ReplaceTscAliasPathsOptions\n): Promise<IConfig> {\n  const output = options.output ?? new Output(options.verbose, options.debug);\n\n  const configFile = !options.configFile\n    ? resolve(process.cwd(), 'tsconfig.json')\n    : !isAbsolute(options.configFile)\n    ? resolve(process.cwd(), options.configFile)\n    : options.configFile;\n\n  output.assert(existsSync(configFile), `Invalid file path => ${configFile}`);\n\n  const {\n    baseUrl = '',\n    outDir,\n    declarationDir,\n    paths,\n    replacers,\n    resolveFullPaths,\n    verbose,\n    fileExtensions\n  } = loadConfig(configFile, output);\n\n  if (options?.fileExtensions?.inputGlob) {\n    fileExtensions.inputGlob = options.fileExtensions.inputGlob;\n  }\n  if (options?.fileExtensions?.outputCheck) {\n    fileExtensions.outputCheck = options.fileExtensions.outputCheck;\n  }\n\n  output.verbose = verbose;\n\n  if (options.resolveFullPaths || resolveFullPaths) {\n    output.debug('resolveFullPaths is active');\n    options.resolveFullPaths = true;\n  }\n\n  const _outDir = options.outDir ?? outDir;\n  if (declarationDir && _outDir !== declarationDir) {\n    options.declarationDir ??= declarationDir;\n  }\n\n  output.assert(_outDir, 'compilerOptions.outDir is not set');\n\n  const configDir: string = normalizePath(dirname(configFile));\n\n  // config with project details and paths\n  const projectConfig: IProjectConfig = {\n    configFile: configFile,\n    baseUrl: baseUrl,\n    outDir: _outDir,\n    configDir: configDir,\n    outPath: _outDir,\n    confDirParentFolderName: basename(configDir),\n    hasExtraModule: false,\n    configDirInOutPath: null,\n    relConfDirPathInOutPath: null,\n    pathCache: new PathCache(!options.watch, fileExtensions?.outputCheck),\n    inputGlob:\n      fileExtensions?.inputGlob || '{mjs,cjs,js,jsx,d.{mts,cts,ts,tsx}}'\n  };\n  output.debug('loaded project config:', projectConfig);\n\n  const config: IConfig = {\n    ...projectConfig,\n    output: output,\n    aliasTrie:\n      options.aliasTrie ?? TrieNode.buildAliasTrie(projectConfig, paths),\n    replacers: []\n  };\n  output.debug('loaded full config:', config);\n\n  // Import replacers.\n  await importReplacers(config, replacers, options.replacers);\n  return config;\n}\n\nfunction replaceConfigDirPlaceholder(path: string, configDir: string) {\n  return path.replace(/\\$\\{configDir\\}/g, configDir);\n}\n\n/**\n * loadConfig loads a config file from fs.\n * @param {string} file file path to the config file that will be loaded.\n * @param {IOutput} output the output instance to log error to.\n * @returns {ITSConfig} a ITSConfig object\n */\nexport const loadConfig = (\n  file: string,\n  output: IOutput,\n  baseConfigDir: string | null = null\n): ITSConfig => {\n  if (!existsSync(file)) {\n    output.error(`File ${file} not found`, true);\n  }\n  output.debug('Loading config file:', file);\n\n  const tsConfig = parseTsconfig(file);\n  const baseTsConfig = Json.loadS<TsConfigJsonResolved>(file, true);\n  const {\n    compilerOptions: { baseUrl, outDir, declarationDir, paths } = {\n      baseUrl: undefined,\n      outDir: undefined,\n      declarationDir: undefined,\n      paths: undefined\n    },\n    'tsc-alias': tscAliasConfig\n  } = tsConfig as TsConfigJsonResolved & { 'tsc-alias': ITSCAliasConfig };\n\n  const configDir = dirname(file);\n  output.debug('configDir', configDir);\n  const config: ITSConfig = {};\n\n  if (baseUrl) {\n    if (baseConfigDir !== null) {\n      config.baseUrl = replaceConfigDirPlaceholder(baseUrl, baseConfigDir);\n    } else {\n      config.baseUrl = baseUrl;\n    }\n  }\n  if (outDir || baseTsConfig?.compilerOptions?.outDir) {\n    let replacedOutDir = outDir || baseTsConfig?.compilerOptions?.outDir;\n    if (baseConfigDir !== null) {\n      replacedOutDir = replaceConfigDirPlaceholder(outDir, baseConfigDir);\n    }\n    config.outDir = isAbsolute(replacedOutDir)\n      ? replacedOutDir\n      : join(configDir, replacedOutDir);\n  }\n  if (paths) {\n    if (baseConfigDir !== null) {\n      for (const key in paths) {\n        paths[key] = paths[key].map((path) =>\n          replaceConfigDirPlaceholder(path, baseConfigDir)\n        );\n      }\n    }\n    config.paths = paths;\n  }\n  if (declarationDir) {\n    let replacedDeclarationDir = declarationDir;\n    if (baseConfigDir !== null) {\n      replacedDeclarationDir = replaceConfigDirPlaceholder(\n        declarationDir,\n        baseConfigDir\n      );\n    }\n    config.declarationDir = isAbsolute(replacedDeclarationDir)\n      ? replacedDeclarationDir\n      : join(configDir, replacedDeclarationDir);\n  }\n  if (tscAliasConfig?.replacers) {\n    config.replacers = tscAliasConfig.replacers;\n  }\n  if (tscAliasConfig?.resolveFullPaths) {\n    config.resolveFullPaths = tscAliasConfig.resolveFullPaths;\n  }\n  if (tscAliasConfig?.verbose) {\n    config.verbose = tscAliasConfig.verbose;\n  }\n  config.fileExtensions = tscAliasConfig?.fileExtensions ?? {};\n\n  const replacerFile = config.replacers?.pathReplacer?.file;\n\n  if (replacerFile) {\n    config.replacers.pathReplacer.file = join(configDir, replacerFile);\n  }\n\n  output.debug('loaded config (from file):', config);\n\n  return config;\n};\n\n/**\n * normalizeTsConfigExtendsOption normalizes tsconfig extends option to a directly loadable path array\n * @param { string|string[] } ext\n * @param { string } file\n * @returns {string[]}\n */\nexport function normalizeTsConfigExtendsOption(\n  ext: string | string[],\n  file: string\n): string[] {\n  if (!ext) return [];\n  const configDir = dirname(file);\n  const normExts = (Array.isArray(ext) ? ext : [ext]).map((e) =>\n    e.startsWith('.')\n      ? join(configDir, e.endsWith('.json') ? e : `${e}.json`)\n      : resolveTsConfigExtendsPath(e, file)\n  );\n  return normExts;\n}\n\n/**\n * resolveTsConfigExtendsPath resolves the path to the config file that is being inherited.\n * @param {string} ext the value of the extends field in the loaded config file.\n * @param {string} file file path to the config file that was loaded.\n * @returns {string} a file path to the config file that is being inherited.\n */\nexport function resolveTsConfigExtendsPath(ext: string, file: string): string {\n  const tsConfigDir = dirname(file);\n  const node_modules: string[] = Dir.nodeModules({ cwd: tsConfigDir }); // Getting all node_modules directories.\n  const targetPaths = node_modules.map((v) => join(tsConfigDir, v, ext)); // Mapping node_modules to target paths.\n\n  // Recursively checking ancestor directories for tsconfig.\n  for (const targetPath of targetPaths) {\n    if (ext.endsWith('.json')) {\n      // Check if the file exists.\n      if (existsSync(targetPath)) {\n        return targetPath;\n      } else {\n        continue; // Continue checking when ext is a file but not yet found.\n      }\n    }\n    let isDirectory = false;\n    try {\n      const stats = lstatSync(targetPath);\n      isDirectory = stats.isDirectory() || stats.isSymbolicLink();\n    } catch (err) {}\n    if (isDirectory) {\n      return join(targetPath, 'tsconfig.json');\n    } else {\n      // When target is not a file nor directory check with '.json' extension.\n      if (existsSync(`${targetPath}.json`)) {\n        return `${targetPath}.json`;\n      }\n    }\n  }\n}\n"]}