{"version": 3, "file": "import-path-resolver.js", "sourceRoot": "", "sources": ["../../src/utils/import-path-resolver.ts"], "names": [], "mappings": ";;;AA2BA,gDAAiD;AACjD,2BAAgC;AAChC,+BAA8C;AAG9C,MAAM,QAAQ,GAAG,MAAM,CAAC;AACxB,MAAM,iBAAiB,GAAG,YAAY,CAAC;AACvC,MAAM,YAAY,GAAG,MAAM,QAAQ,GAAG,iBAAiB,GAAG,QAAQ,GAAG,CAAC;AAKtE,MAAM,SAAS,GAAG,2DAA2D,YAAY,UAAU,CAAC;AACpG,MAAM,WAAW,GAAG,mBAAmB,YAAY,GAAG,CAAC;AACvD,MAAM,oBAAoB,GAAG,eAAe,YAAY,GAAG,CAAC;AAC5D,MAAM,SAAS,GAAG,iBAAiB,YAAY,GAAG,CAAC;AACnD,MAAM,kBAAkB,GAAG,aAAa,YAAY,GAAG,CAAC;AACxD,MAAM,WAAW,GAAG,mBAAmB,YAAY,GAAG,CAAC;AAEvD,MAAM,iBAAiB,GAAG,MAAM;IAC9B,SAAS;IACT,WAAW;IACX,oBAAoB;IACpB,SAAS;IACT,kBAAkB;IAClB,WAAW;CACZ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AAEf,MAAM,kBAAkB;IACtB,YAAmB,MAAc,EAAW,UAAkB;QAA3C,WAAM,GAAN,MAAM,CAAQ;QAAW,eAAU,GAAV,UAAU,CAAQ;IAAG,CAAC;IAElE,IAAI,SAAS;QACX,OAAO,IAAA,cAAO,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAClC,CAAC;IAMD,wBAAwB,CAAC,QAAwB;QAC/C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAC/B,kBAAkB,CAAC,uBAAuB,CAAC,GAAG,CAAC,EAC/C,QAAQ,CACT,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IAOD,sBAAsB,CAAC,GAAG,GAAG,KAAK;QAChC,IAAI,CAAC,wBAAwB,CAAC,CAAC,eAAe,EAAE,EAAE;YAEhD,MAAM,eAAe,GAAG,eAAe,CAAC,KAAK,CAAC,IAAA,sBAAc,GAAE,CAAC,CAAC;YAChE,IAAI,CAAC,eAAe,EAAE;gBACpB,OAAO,eAAe,CAAC;aACxB;YACD,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC;YACxD,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YAChE,OAAO,eAAe,CAAC,OAAO,CAC5B,cAAc,EACd,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CACvC,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAMO,eAAe,CAAC,UAAkB,EAAE,GAAG,GAAG,KAAK;QAErD,IACE,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC;YAC3B,UAAU,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,CAAC,EACxC;YACA,OAAO,UAAU,CAAC;SACnB;QAED,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;YAC/B,MAAM,UAAU,GAAG,GAAG,UAAU,GAAG,GAAG,EAAE,CAAC;YACzC,IAAI,IAAA,eAAU,EAAC,IAAA,cAAO,EAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE;gBACnD,OAAO,UAAU,CAAC;aACnB;SACF;QAED,IAAI,UAAU,GAAG,IAAA,WAAI,EAAC,UAAU,EAAE,OAAO,GAAG,GAAG,CAAC,CAAC;QACjD,IACE,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,UAAU,KAAK,GAAG,CAAC;YACnD,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAC5B;YACA,UAAU,GAAG,IAAI,GAAG,UAAU,CAAC;SAChC;QACD,OAAO,IAAA,eAAU,EAAC,IAAA,cAAO,EAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YACpD,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,UAAU,CAAC;IACjB,CAAC;IAED,MAAM,CAAC,cAAc;QACnB,OAAO,IAAI,MAAM,CACf,qBAAqB,QAAQ,WAAW,iBAAiB,IAAI,QAAQ,GAAG,CACzE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,KAAK,GAAG,EAAE;QACvC,OAAO,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,MAAM,CAAC,sBAAsB,CAAC,IAAY,EAAE,IAAY,EAAE,GAAG,GAAG,KAAK;QACnE,OAAO,IAAI,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,sBAAsB,CAAC,GAAG,CAAC;aAClE,MAAM,CAAC;IACZ,CAAC;IAED,MAAM,CAAC,wBAAwB,CAC7B,IAAY,EACZ,IAAY,EACZ,QAAwB;QAExB,OAAO,IAAI,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,wBAAwB,CAAC,QAAQ,CAAC;aACzE,MAAM,CAAC;IACZ,CAAC;CACF;AAIY,QAAA,sBAAsB,GAAG,kBAAkB,CAAC,sBAAsB,CAAC;AACnE,QAAA,uBAAuB,GAClC,kBAAkB,CAAC,uBAAuB,CAAC;AAChC,QAAA,wBAAwB,GACnC,kBAAkB,CAAC,wBAAwB,CAAC;AACjC,QAAA,cAAc,GAAG,kBAAkB,CAAC,cAAc,CAAC", "sourcesContent": ["/**\n * @file\n *\n * Import statements come in a lot of flavors, so having a single\n * regex that can capture all of those with minimal side effects\n * is trickly. In this file this regex is constructed from multiple parts.\n *\n * Using a named captured group (supported in ES2018/Node 10+)\n * to allow arbitrary complexity of the regex without worrying\n * about messing up indexing.\n *\n * Meant to match ESM/CommonJS import patterns.\n *\n * ⚠ Can match content of strings and comments!\n *\n * @example\n * // Examples of import statements that must be matched\n * // (Note that there could be newlines between tokens.)\n * const module = require('some/path')\n * import module from 'some/path'\n * import \"some/path\"\n * import theDefault, {namedExport} from 'some/path'\n * const asyncImport = await import('some/path')\n * export * from 'some/path';\n */\n\n/** */\nimport normalizePath = require('normalize-path');\nimport { existsSync } from 'fs';\nimport { dirname, join, resolve } from 'path';\nimport { StringReplacer } from '../interfaces';\n\nconst anyQuote = `[\"']`;\nconst pathStringContent = `[^\"'\\r\\n]+`;\nconst importString = `(?:${anyQuote}${pathStringContent}${anyQuote})`;\n\n// Separate patterns for each style of import statement,\n// wrapped in non-capturing groups,\n// so that they can be strung together in one big pattern.\nconst funcStyle = `(?:\\\\b(?:import|require)\\\\s*\\\\(\\\\s*(\\\\/\\\\*.*\\\\*\\\\/\\\\s*)?${importString}\\\\s*\\\\))`;\nconst globalStyle = `(?:\\\\bimport\\\\s+${importString})`;\nconst globalMinimizedStyle = `(?:\\\\bimport${importString})`;\nconst fromStyle = `(?:\\\\bfrom\\\\s+${importString})`;\nconst fromMinimizedStyle = `(?:\\\\bfrom${importString})`;\nconst moduleStyle = `(?:\\\\bmodule\\\\s+${importString})`;\n\nconst importRegexString = `(?:${[\n  funcStyle,\n  globalStyle,\n  globalMinimizedStyle,\n  fromStyle,\n  fromMinimizedStyle,\n  moduleStyle\n].join(`|`)})`;\n\nclass ImportPathResolver {\n  constructor(public source: string, readonly sourcePath: string) {}\n\n  get sourceDir() {\n    return dirname(this.sourcePath);\n  }\n\n  /**\n   * Replace all source import paths, using a replacer\n   * function (a la `String.prototype.replace(globalRegex,replacer)`)\n   */\n  replaceSourceImportPaths(replacer: StringReplacer) {\n    this.source = this.source.replace(\n      ImportPathResolver.newImportStatementRegex('g'),\n      replacer\n    );\n    return this;\n  }\n\n  /**\n   * For a JavaScript code string, find all local import paths\n   * and resolve them to full filenames (including the .js extension).\n   * If no matching file is found for a path, leave it alone.\n   */\n  resolveFullImportPaths(ext = '.js') {\n    this.replaceSourceImportPaths((importStatement) => {\n      // Find substring that is just quotes\n      const importPathMatch = importStatement.match(newStringRegex());\n      if (!importPathMatch) {\n        return importStatement;\n      }\n      const { path, pathWithQuotes } = importPathMatch.groups;\n      const fullPath = normalizePath(this.resolveFullPath(path, ext));\n      return importStatement.replace(\n        pathWithQuotes,\n        pathWithQuotes.replace(path, fullPath)\n      );\n    });\n    return this;\n  }\n\n  /**\n   * Given an import path, resolve the full path (including extension).\n   * If no corresponding file can be found, return the original path.\n   */\n  private resolveFullPath(importPath: string, ext = '.js') {\n    // If bare import or already a full path import\n    if (\n      !importPath.startsWith('.') ||\n      importPath.match(new RegExp(`\\${ext}$`))\n    ) {\n      return importPath;\n    }\n    // Try adding the extension (if not obviously a directory)\n    if (!importPath.match(/[/\\\\]$/)) {\n      const asFilePath = `${importPath}${ext}`;\n      if (existsSync(resolve(this.sourceDir, asFilePath))) {\n        return asFilePath;\n      }\n    }\n    // Assume the path is a folder; try adding index.js\n    let asFilePath = join(importPath, 'index' + ext);\n    if (\n      (importPath.startsWith('./') || importPath === '.') &&\n      !asFilePath.startsWith('./')\n    ) {\n      asFilePath = './' + asFilePath;\n    }\n    return existsSync(resolve(this.sourceDir, asFilePath))\n      ? asFilePath\n      : importPath;\n  }\n\n  static newStringRegex() {\n    return new RegExp(\n      `(?<pathWithQuotes>${anyQuote}(?<path>${pathStringContent})${anyQuote})`\n    );\n  }\n\n  static newImportStatementRegex(flags = '') {\n    return new RegExp(importRegexString, flags);\n  }\n\n  static resolveFullImportPaths(code: string, path: string, ext = '.js') {\n    return new ImportPathResolver(code, path).resolveFullImportPaths(ext)\n      .source;\n  }\n\n  static replaceSourceImportPaths(\n    code: string,\n    path: string,\n    replacer: StringReplacer\n  ) {\n    return new ImportPathResolver(code, path).replaceSourceImportPaths(replacer)\n      .source;\n  }\n}\n\n// Export aliases for the static functions\n// to make usage more friendly.\nexport const resolveFullImportPaths = ImportPathResolver.resolveFullImportPaths;\nexport const newImportStatementRegex =\n  ImportPathResolver.newImportStatementRegex;\nexport const replaceSourceImportPaths =\n  ImportPathResolver.replaceSourceImportPaths;\nexport const newStringRegex = ImportPathResolver.newStringRegex;\n"]}