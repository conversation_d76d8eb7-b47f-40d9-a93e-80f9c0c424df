{"name": "@fastify/swagger-ui", "version": "2.1.0", "description": "<PERSON><PERSON>-ui for Fastify", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"coverage": "npm run unit -- --coverage-report=lcovonly", "lint": "standard", "lint:fix": "npm run lint -- --fix", "prepare": "node scripts/prepare-swagger-ui", "prepublishOnly": "npm run prepare", "test": "npm run prepare && npm run coverage && npm run typescript", "test:dev": "npm run lint && npm run unit && npm run typescript", "test:e2e:command": "node ./examples/example-e2e.js", "test:e2e": "npx playwright test", "test:e2e:ui": "npx playwright test --ui", "typescript": "tsd", "unit": "tap", "unit:report": "npm run unit -- --coverage-report=html", "unit:verbose": "npm run unit -- -Rspec"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-swagger-ui.git"}, "keywords": ["fastify", "swagger", "openapi", "swagger-ui", "serve", "static"], "author": "<PERSON> - @delvedor (http://delved.org)", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-swagger-ui/issues"}, "homepage": "https://github.com/fastify/fastify-swagger-ui#readme", "devDependencies": {"@apidevtools/swagger-parser": "^10.1.0", "@fastify/basic-auth": "^5.0.0", "@fastify/helmet": "^11.0.0", "@fastify/pre-commit": "^2.0.2", "@fastify/swagger": "^8.0.0", "@playwright/test": "^1.39.0", "@types/node": "^20.1.1", "ajv": "^8.11.0", "fastify": "^4.0.0", "fs-extra": "^11.0.0", "qs": "^6.11.0", "standard": "^17.0.0", "swagger-ui-dist": "5.10.5", "tap": "^16.3.2", "tsd": "^0.30.0"}, "dependencies": {"@fastify/static": "^6.0.0", "fastify-plugin": "^4.0.0", "openapi-types": "^12.0.2", "rfdc": "^1.3.0", "yaml": "^2.2.2"}, "standard": {"ignore": ["dist"]}, "tsd": {"directory": "types"}, "pkg": {"assets": ["static/**/*"]}, "publishConfig": {"access": "public"}, "pre-commit": ["lint", "test"]}