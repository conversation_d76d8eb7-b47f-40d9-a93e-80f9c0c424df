name: CI

on:
  push:
    branches:
     - main
     - master
     - next
     - 'v*'
    paths-ignore:
      - 'docs/**'
      - '*.md'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '*.md'

jobs:
  e2e:
    uses: ./.github/workflows/playwright.yml

  test:
    uses: fastify/workflows/.github/workflows/plugins-ci.yml@v3
    needs: e2e
    with:
      license-check: true
      lint: true
