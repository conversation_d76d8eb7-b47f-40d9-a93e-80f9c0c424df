name: Playwright Tests

on:
  workflow_dispatch:
  workflow_call:

jobs:
  test:
    runs-on: ubuntu-latest
    permissions:
      contents: read

    timeout-minutes: 60

    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: lts/*
    - name: Install dependencies
      run: npm i
    - name: Install Playwright Browsers
      run: npx playwright@1 install chromium --with-deps
    - name: Run Playwright tests
      run: npx playwright@1 test
