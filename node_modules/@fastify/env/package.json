{"name": "@fastify/env", "version": "4.4.0", "description": "Fastify plugin to check environment variables", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"coverage": "cross-env VALUE_FROM_ENV=pippo tap --cov --coverage-report=html", "lint": "standard | snazzy", "lint:fix": "standard --fix", "test": "npm run test:unit && npm run test:typescript", "test:typescript": "tsd", "test:unit": "cross-env VALUE_FROM_ENV=pippo tap"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-env.git"}, "keywords": ["fastify"], "author": "<PERSON><PERSON><PERSON> - @allevo", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-env/issues"}, "homepage": "https://github.com/fastify/fastify-env#readme", "dependencies": {"env-schema": "^6.0.0", "fastify-plugin": "^4.0.0"}, "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@types/node": "^20.1.0", "cross-env": "^7.0.2", "fastify": "^4.0.0-rc.2", "snazzy": "^9.0.0", "standard": "^17.0.0", "tap": "^16.0.0", "tsd": "^0.31.0"}, "pre-commit": ["lint", "test"], "tsd": {"directory": "test/types"}, "publishConfig": {"access": "public"}}