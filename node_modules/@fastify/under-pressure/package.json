{"name": "@fastify/under-pressure", "version": "8.5.2", "description": "Measure process load with automatic handling of 'Service Unavailable' plugin for Fastify.", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "standard | snazzy", "test": "npm run test:unit && npm run test:typescript", "test:unit": "tap", "test:typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/under-pressure.git"}, "bugs": {"url": "https://github.com/fastify/under-pressure/issues"}, "keywords": ["fastify", "service unavailable", "limit", "delay", "retry"], "author": "<PERSON> - @delvedor (http://delved.org)", "license": "MIT", "dependencies": {"@fastify/error": "^3.0.0", "fastify-plugin": "^4.0.0"}, "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@types/node": "^20.1.0", "fastify": "^4.0.0-rc.2", "semver": "^7.3.2", "simple-get": "^4.0.0", "sinon": "^18.0.0", "snazzy": "^9.0.0", "standard": "^17.0.0", "tap": "^16.2.0", "tsd": "^0.31.0"}, "publishConfig": {"access": "public"}}