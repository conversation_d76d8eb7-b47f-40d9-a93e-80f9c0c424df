'use strict'

const http = require('http')
const send = require('..')

module.exports.shouldNotHaveHeader = function shouldNotHaveHeader (header, t) {
  return function (res) {
    t.ok(!(header.toLowerCase() in res.headers), 'should not have header ' + header)
  }
}

module.exports.createServer = function createServer (opts, fn) {
  return http.createServer(function onRequest (req, res) {
    try {
      fn && fn(req, res)
      send(req, req.url, opts).pipe(res)
    } catch (err) {
      res.statusCode = 500
      res.end(String(err))
    }
  })
}

module.exports.shouldNotHaveBody = function shouldNotHaveBody (t) {
  return function (res) {
    t.ok(res.text === '' || res.text === undefined)
  }
}
