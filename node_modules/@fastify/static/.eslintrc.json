{"plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "standard"], "overrides": [{"files": ["types/*.test-d.ts", "types/*.d.ts"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": ["./tsconfig.eslint.json"]}, "extends": ["plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "rules": {"no-use-before-define": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-floating-promises": "off", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-misused-promises": ["error", {"checksVoidReturn": false}]}}]}