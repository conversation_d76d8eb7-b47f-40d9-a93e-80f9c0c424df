{"name": "@fastify/swagger", "version": "8.15.0", "description": "Serve Swagger/OpenAPI documentation for Fastify, supporting dynamic generation", "main": "index.js", "type": "commonjs", "types": "index.d.ts", "scripts": {"lint": "standard", "lint:fix": "standard --fix", "test": "npm run test:unit && npm run test:typescript", "test:typescript": "tsd", "test:unit": "tap", "test:unit:report": "npm run test:unit -- --coverage-report=html", "test:unit:verbose": "npm run test:unit -- -Rspec"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-swagger.git"}, "keywords": ["fastify", "swagger", "openapi", "serve", "generate", "static"], "author": "<PERSON> - @delvedor (http://delved.org)", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-swagger/issues"}, "homepage": "https://github.com/fastify/fastify-swagger#readme", "devDependencies": {"@apidevtools/swagger-parser": "^10.1.0", "@fastify/cookie": "^9.0.4", "@fastify/pre-commit": "^2.0.2", "@types/node": "^20.1.0", "fastify": "^4.0.0", "fluent-json-schema": "^5.0.0", "joi": "^17.6.0", "joi-to-json": "^4.0.0", "qs": "^6.10.3", "standard": "^17.0.0", "tap": "^16.2.0", "tsd": "^0.31.0"}, "dependencies": {"fastify-plugin": "^4.0.0", "json-schema-resolver": "^2.0.0", "openapi-types": "^12.0.0", "rfdc": "^1.3.0", "yaml": "^2.2.2"}, "standard": {"ignore": ["static", "tap-snapshots/*"]}, "tsd": {"directory": "test/types"}, "publishConfig": {"access": "public"}, "pre-commit": ["lint", "test"]}