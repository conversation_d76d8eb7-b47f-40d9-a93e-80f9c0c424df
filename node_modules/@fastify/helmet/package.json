{"name": "@fastify/helmet", "version": "11.1.1", "description": "Important security headers for Fastify", "main": "index.js", "types": "types/index.d.ts", "scripts": {"coverage": "npm run unit -- --coverage-report=lcovonly", "lint": "standard | snazzy", "lint:fix": "standard --fix | snazzy", "test": "npm run lint && npm run unit && npm run typescript", "test:ci": "npm run lint && npm run coverage && npm run typescript", "unit": "tap -J \"test/*.test.js\"", "unit:report": "npm run unit -- --coverage-report=html", "unit:verbose": "npm run unit -- -Rspec", "typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-helmet.git"}, "keywords": ["fastify", "helmet", "security", "headers", "x-frame-options", "csp", "hsts", "clickjack"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify-helmet/issues"}, "homepage": "https://github.com/fastify/fastify-helmet#readme", "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@types/node": "^20.1.2", "fastify": "^4.7.0", "snazzy": "^9.0.0", "standard": "^17.0.0", "tap": "^16.0.0", "tsd": "^0.29.0"}, "dependencies": {"fastify-plugin": "^4.2.1", "helmet": "^7.0.0"}, "tsd": {"directory": "test/types"}, "publishConfig": {"access": "public"}}