{"name": "plimit-lit", "description": "This package is a helper to run multiple promise-returning & async functions with limited concurrency.", "version": "1.6.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/joelvoss/plimit-lit", "bugs": {"url": "https://github.com/joelvoss/plimit-lit/issues"}, "repository": {"type": "git", "url": "git+https://github.com/joelvoss/plimit-lit.git"}, "engines": {"node": ">=12"}, "type": "module", "source": "src/index.js", "main": "dist/plimit-lit.cjs", "module": "dist/plimit-lit.module.js", "exports": {"types": "./dist/index.d.ts", "require": "./dist/plimit-lit.cjs", "import": "./dist/plimit-lit.modern.js"}, "types": "dist/index.d.ts", "files": ["dist", "LICENSE"], "scripts": {"start": "./Taskfile.sh", "test": "./Taskfile.sh test", "prepublishOnly": "./Taskfile.sh build"}, "dependencies": {"queue-lit": "^1.5.1"}, "devDependencies": {"@jvdx/core": "^3.6.0"}, "prettier": "@jvdx/prettier-config", "prettierIgnore": ["tests/", "dist/"], "eslintConfig": {"extends": "@jvdx/eslint-config"}, "eslintIgnore": ["tests/", "dist/"]}