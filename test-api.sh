#!/bin/bash

# AI Proxy Service API 测试脚本

echo "🚀 AI Proxy Service API 测试"
echo "================================"

BASE_URL="http://localhost:3000"

echo ""
echo "📋 1. 检查服务状态"
echo "GET $BASE_URL/"
curl -s "$BASE_URL/" | jq .

echo ""
echo "🏥 2. 健康检查"
echo "GET $BASE_URL/health"
curl -s "$BASE_URL/health" | jq .

echo ""
echo "📊 3. 获取指标"
echo "GET $BASE_URL/metrics"
curl -s "$BASE_URL/metrics" | jq .

echo ""
echo "🤖 4. 获取可用模型"
echo "GET $BASE_URL/v1/models"
curl -s "$BASE_URL/v1/models" | jq '.data[] | {id: .id, provider: .provider, enabled: .enabled}'

echo ""
echo "🔍 5. 获取特定模型信息"
echo "GET $BASE_URL/v1/models/gpt-4o"
curl -s "$BASE_URL/v1/models/gpt-4o" | jq .

echo ""
echo "💬 6. 测试聊天完成 (需要真实 API 密钥)"
echo "POST $BASE_URL/v1/chat/completions"
echo "注意：此测试需要在 .env 文件中配置真实的 API 密钥"

# 测试请求 (注释掉，因为需要真实 API 密钥)
# curl -s -X POST "$BASE_URL/v1/chat/completions" \
#   -H "Content-Type: application/json" \
#   -d '{
#     "model": "gpt-4o",
#     "messages": [
#       {"role": "user", "content": "Hello, world!"}
#     ],
#     "max_tokens": 50
#   }' | jq .

echo ""
echo "🌊 7. 测试流式响应 (需要真实 API 密钥)"
echo "POST $BASE_URL/v1/chat/completions (stream=true)"
echo "注意：此测试需要在 .env 文件中配置真实的 API 密钥"

# 流式测试请求 (注释掉，因为需要真实 API 密钥)
# curl -s -X POST "$BASE_URL/v1/chat/completions" \
#   -H "Content-Type: application/json" \
#   -d '{
#     "model": "gpt-4o",
#     "messages": [
#       {"role": "user", "content": "Count from 1 to 5"}
#     ],
#     "stream": true,
#     "max_tokens": 50
#   }'

echo ""
echo "✅ API 测试完成！"
echo ""
echo "📝 使用说明："
echo "1. 要测试实际的 AI 聊天功能，请在 .env 文件中配置真实的 API 密钥"
echo "2. 访问 http://localhost:3000/docs 查看完整的 API 文档"
echo "3. 服务支持 OpenAI 兼容的 API 格式"
echo "4. 支持流式和非流式响应模式"
