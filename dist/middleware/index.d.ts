import { FastifyRequest, FastifyReply, FastifyInstance } from 'fastify';
/**
 * Request ID middleware - adds unique request ID to each request
 */
export declare const requestIdMiddleware: (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
/**
 * Request logging middleware
 */
export declare const requestLoggingMiddleware: (request: FastifyRequest, _reply: FastifyReply) => Promise<void>;
/**
 * Error handling middleware
 */
export declare const errorHandlerMiddleware: (error: Error, request: FastifyRequest, reply: FastifyReply) => FastifyReply<import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, import("fastify").RouteGenericInterface, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
/**
 * CORS middleware configuration
 */
export declare const corsMiddleware: {
    origin: (origin: string, callback: (err: Error | null, allow?: boolean) => void) => void;
    credentials: boolean;
    methods: string[];
    allowedHeaders: string[];
    exposedHeaders: string[];
};
/**
 * Security headers middleware
 */
export declare const securityMiddleware: {
    contentSecurityPolicy: {
        directives: {
            defaultSrc: string[];
            styleSrc: string[];
            scriptSrc: string[];
            imgSrc: string[];
            connectSrc: string[];
            fontSrc: string[];
            objectSrc: string[];
            mediaSrc: string[];
            frameSrc: string[];
        };
    };
    crossOriginEmbedderPolicy: boolean;
    crossOriginOpenerPolicy: boolean;
    crossOriginResourcePolicy: boolean;
    dnsPrefetchControl: boolean;
    frameguard: {
        action: string;
    };
    hidePoweredBy: boolean;
    hsts: {
        maxAge: number;
        includeSubDomains: boolean;
        preload: boolean;
    };
    ieNoOpen: boolean;
    noSniff: boolean;
    originAgentCluster: boolean;
    permittedCrossDomainPolicies: boolean;
    referrerPolicy: string;
    xssFilter: boolean;
};
/**
 * Rate limiting configuration
 */
export declare const rateLimitMiddleware: {
    max: number;
    timeWindow: string;
    skipOnError: boolean;
    skipSuccessfulRequests: boolean;
    keyGenerator: (request: FastifyRequest) => string;
    errorResponseBuilder: (request: FastifyRequest, context: any) => {
        error: {
            type: string;
            message: string;
            code: string;
            retryAfter: number;
        };
        requestId: any;
    };
    addHeaders: {
        'x-ratelimit-limit': boolean;
        'x-ratelimit-remaining': boolean;
        'x-ratelimit-reset': boolean;
    };
};
/**
 * Health check middleware for load balancers
 */
export declare const healthCheckMiddleware: (_request: FastifyRequest, reply: FastifyReply) => Promise<never>;
/**
 * Request size limit middleware
 */
export declare const requestSizeLimitMiddleware: {
    bodyLimit: number;
};
/**
 * Compression middleware configuration
 */
export declare const compressionMiddleware: {
    global: boolean;
    threshold: number;
    encodings: string[];
};
/**
 * Register all middleware with Fastify instance
 */
export declare const registerMiddleware: (fastify: FastifyInstance) => Promise<void>;
declare const _default: {
    requestIdMiddleware: (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
    requestLoggingMiddleware: (request: FastifyRequest, _reply: FastifyReply) => Promise<void>;
    errorHandlerMiddleware: (error: Error, request: FastifyRequest, reply: FastifyReply) => FastifyReply<import("fastify").RawServerDefault, import("http").IncomingMessage, import("http").ServerResponse<import("http").IncomingMessage>, import("fastify").RouteGenericInterface, unknown, import("fastify").FastifySchema, import("fastify").FastifyTypeProviderDefault, unknown>;
    corsMiddleware: {
        origin: (origin: string, callback: (err: Error | null, allow?: boolean) => void) => void;
        credentials: boolean;
        methods: string[];
        allowedHeaders: string[];
        exposedHeaders: string[];
    };
    securityMiddleware: {
        contentSecurityPolicy: {
            directives: {
                defaultSrc: string[];
                styleSrc: string[];
                scriptSrc: string[];
                imgSrc: string[];
                connectSrc: string[];
                fontSrc: string[];
                objectSrc: string[];
                mediaSrc: string[];
                frameSrc: string[];
            };
        };
        crossOriginEmbedderPolicy: boolean;
        crossOriginOpenerPolicy: boolean;
        crossOriginResourcePolicy: boolean;
        dnsPrefetchControl: boolean;
        frameguard: {
            action: string;
        };
        hidePoweredBy: boolean;
        hsts: {
            maxAge: number;
            includeSubDomains: boolean;
            preload: boolean;
        };
        ieNoOpen: boolean;
        noSniff: boolean;
        originAgentCluster: boolean;
        permittedCrossDomainPolicies: boolean;
        referrerPolicy: string;
        xssFilter: boolean;
    };
    rateLimitMiddleware: {
        max: number;
        timeWindow: string;
        skipOnError: boolean;
        skipSuccessfulRequests: boolean;
        keyGenerator: (request: FastifyRequest) => string;
        errorResponseBuilder: (request: FastifyRequest, context: any) => {
            error: {
                type: string;
                message: string;
                code: string;
                retryAfter: number;
            };
            requestId: any;
        };
        addHeaders: {
            'x-ratelimit-limit': boolean;
            'x-ratelimit-remaining': boolean;
            'x-ratelimit-reset': boolean;
        };
    };
    healthCheckMiddleware: (_request: FastifyRequest, reply: FastifyReply) => Promise<never>;
    requestSizeLimitMiddleware: {
        bodyLimit: number;
    };
    compressionMiddleware: {
        global: boolean;
        threshold: number;
        encodings: string[];
    };
    registerMiddleware: (fastify: FastifyInstance) => Promise<void>;
};
export default _default;
//# sourceMappingURL=index.d.ts.map