"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerMiddleware = exports.compressionMiddleware = exports.requestSizeLimitMiddleware = exports.healthCheckMiddleware = exports.rateLimitMiddleware = exports.securityMiddleware = exports.corsMiddleware = exports.errorHandlerMiddleware = exports.requestLoggingMiddleware = exports.requestIdMiddleware = void 0;
const utils_1 = require("@/utils");
const logger_1 = require("@/utils/logger");
const types_1 = require("@/types");
/**
 * Request ID middleware - adds unique request ID to each request
 */
const requestIdMiddleware = async (request, reply) => {
    const requestId = (0, utils_1.generateRequestId)();
    request.headers['x-request-id'] = requestId;
    reply.header('x-request-id', requestId);
    // Store request ID for logging
    request.requestId = requestId;
};
exports.requestIdMiddleware = requestIdMiddleware;
/**
 * Request logging middleware
 */
const requestLoggingMiddleware = async (request, reply) => {
    const startTime = (0, utils_1.getPerformanceTimestamp)();
    const requestId = request.requestId || 'unknown';
    // Log incoming request
    (0, logger_1.logRequest)(requestId, request.method, request.url, request.headers['user-agent']);
    // Store start time for response logging in request context
    request.startTime = startTime;
};
exports.requestLoggingMiddleware = requestLoggingMiddleware;
/**
 * Error handling middleware
 */
const errorHandlerMiddleware = (error, request, reply) => {
    const requestId = request.requestId || 'unknown';
    // Log the error
    (0, logger_1.logError)(error, requestId);
    // Handle different error types
    if (error instanceof types_1.ValidationError) {
        return reply.status(400).send({
            error: {
                type: 'validation_error',
                message: error.message,
                code: 'VALIDATION_ERROR',
            },
            requestId,
        });
    }
    if (error instanceof types_1.RateLimitError) {
        return reply.status(429).send({
            error: {
                type: 'rate_limit_error',
                message: error.message,
                code: 'RATE_LIMIT_EXCEEDED',
            },
            requestId,
        });
    }
    if (error instanceof types_1.ProviderError) {
        return reply.status(error.statusCode).send({
            error: {
                type: 'provider_error',
                message: error.message,
                provider: error.provider,
                code: 'PROVIDER_ERROR',
            },
            requestId,
        });
    }
    if (error instanceof types_1.AIProxyError) {
        return reply.status(error.statusCode).send({
            error: {
                type: 'ai_proxy_error',
                message: error.message,
                code: 'AI_PROXY_ERROR',
            },
            requestId,
        });
    }
    // Handle Fastify validation errors
    if (error.name === 'FastifyError' && error.validation) {
        return reply.status(400).send({
            error: {
                type: 'validation_error',
                message: 'Request validation failed',
                details: error.validation,
                code: 'REQUEST_VALIDATION_ERROR',
            },
            requestId,
        });
    }
    // Handle timeout errors
    if (error.message.includes('timeout') || error.name === 'TimeoutError') {
        return reply.status(408).send({
            error: {
                type: 'timeout_error',
                message: 'Request timeout',
                code: 'REQUEST_TIMEOUT',
            },
            requestId,
        });
    }
    // Handle unknown errors
    logger_1.logger.error('Unhandled error', {
        error: {
            name: error.name,
            message: error.message,
            stack: error.stack,
        },
        requestId,
        url: request.url,
        method: request.method,
    });
    return reply.status(500).send({
        error: {
            type: 'internal_error',
            message: 'Internal server error',
            code: 'INTERNAL_ERROR',
        },
        requestId,
    });
};
exports.errorHandlerMiddleware = errorHandlerMiddleware;
/**
 * CORS middleware configuration
 */
exports.corsMiddleware = {
    origin: (origin, callback) => {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin)
            return callback(null, true);
        // In production, you should validate against allowed origins
        // For now, we'll allow all origins as configured
        return callback(null, true);
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'X-Request-ID',
        'Accept',
        'Origin',
    ],
    exposedHeaders: ['X-Request-ID'],
};
/**
 * Security headers middleware
 */
exports.securityMiddleware = {
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
        },
    },
    crossOriginEmbedderPolicy: false, // Disable for API server
    crossOriginOpenerPolicy: false, // Disable for API server
    crossOriginResourcePolicy: false, // Disable for API server
    dnsPrefetchControl: true,
    frameguard: { action: 'deny' },
    hidePoweredBy: true,
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
    },
    ieNoOpen: true,
    noSniff: true,
    originAgentCluster: true,
    permittedCrossDomainPolicies: false,
    referrerPolicy: 'no-referrer',
    xssFilter: true,
};
/**
 * Rate limiting configuration
 */
exports.rateLimitMiddleware = {
    max: 100, // requests per timeWindow
    timeWindow: '1 minute',
    skipOnError: true,
    skipSuccessfulRequests: false,
    keyGenerator: (request) => {
        // Use IP address as the key
        return request.ip;
    },
    errorResponseBuilder: (request, context) => {
        const requestId = request.requestId || 'unknown';
        return {
            error: {
                type: 'rate_limit_error',
                message: `Rate limit exceeded. Try again in ${Math.round(context.ttl / 1000)} seconds.`,
                code: 'RATE_LIMIT_EXCEEDED',
                retryAfter: Math.round(context.ttl / 1000),
            },
            requestId,
        };
    },
    addHeaders: {
        'x-ratelimit-limit': true,
        'x-ratelimit-remaining': true,
        'x-ratelimit-reset': true,
    },
};
/**
 * Health check middleware for load balancers
 */
const healthCheckMiddleware = async (_request, reply) => {
    // Simple health check - just return 200 OK
    return reply.status(200).send({ status: 'ok', timestamp: new Date().toISOString() });
};
exports.healthCheckMiddleware = healthCheckMiddleware;
/**
 * Request size limit middleware
 */
exports.requestSizeLimitMiddleware = {
    bodyLimit: 10 * 1024 * 1024, // 10MB limit
};
/**
 * Compression middleware configuration
 */
exports.compressionMiddleware = {
    global: true,
    threshold: 1024, // Only compress responses larger than 1KB
    encodings: ['gzip', 'deflate'],
};
/**
 * Register all middleware with Fastify instance
 */
const registerMiddleware = async (fastify) => {
    // Request ID middleware (first)
    fastify.addHook('preHandler', exports.requestIdMiddleware);
    // Request logging middleware
    fastify.addHook('preHandler', exports.requestLoggingMiddleware);
    // Error handler
    fastify.setErrorHandler(exports.errorHandlerMiddleware);
    // Health check route (before rate limiting)
    fastify.get('/health', exports.healthCheckMiddleware);
    fastify.get('/ping', exports.healthCheckMiddleware);
    logger_1.logger.info('All middleware registered successfully');
};
exports.registerMiddleware = registerMiddleware;
exports.default = {
    requestIdMiddleware: exports.requestIdMiddleware,
    requestLoggingMiddleware: exports.requestLoggingMiddleware,
    errorHandlerMiddleware: exports.errorHandlerMiddleware,
    corsMiddleware: exports.corsMiddleware,
    securityMiddleware: exports.securityMiddleware,
    rateLimitMiddleware: exports.rateLimitMiddleware,
    healthCheckMiddleware: exports.healthCheckMiddleware,
    requestSizeLimitMiddleware: exports.requestSizeLimitMiddleware,
    compressionMiddleware: exports.compressionMiddleware,
    registerMiddleware: exports.registerMiddleware,
};
//# sourceMappingURL=index.js.map