{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/middleware/index.ts"], "names": [], "mappings": ";;;AACA,mCAAwF;AACxF,2CAA2E;AAC3E,mCAAuF;AAEvF;;GAEG;AACI,MAAM,mBAAmB,GAAG,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;IACxF,MAAM,SAAS,GAAG,IAAA,yBAAiB,GAAE,CAAC;IACtC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,SAAS,CAAC;IAC5C,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IAExC,+BAA+B;IAC9B,OAAe,CAAC,SAAS,GAAG,SAAS,CAAC;AACzC,CAAC,CAAC;AAPW,QAAA,mBAAmB,uBAO9B;AAEF;;GAEG;AACI,MAAM,wBAAwB,GAAG,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;IAC7F,MAAM,SAAS,GAAG,IAAA,+BAAuB,GAAE,CAAC;IAC5C,MAAM,SAAS,GAAI,OAAe,CAAC,SAAS,IAAI,SAAS,CAAC;IAE1D,uBAAuB;IACvB,IAAA,mBAAU,EACR,SAAS,EACT,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,GAAG,EACX,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAC9B,CAAC;IAIF,2DAA2D;IAC1D,OAAe,CAAC,SAAS,GAAG,SAAS,CAAC;AACzC,CAAC,CAAC;AAhBW,QAAA,wBAAwB,4BAgBnC;AAEF;;GAEG;AACI,MAAM,sBAAsB,GAAG,CACpC,KAAY,EACZ,OAAuB,EACvB,KAAmB,EACnB,EAAE;IACF,MAAM,SAAS,GAAI,OAAe,CAAC,SAAS,IAAI,SAAS,CAAC;IAE1D,gBAAgB;IAChB,IAAA,iBAAQ,EAAC,KAAK,EAAE,SAAS,CAAC,CAAC;IAE3B,+BAA+B;IAC/B,IAAI,KAAK,YAAY,uBAAe,EAAE,CAAC;QACrC,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC5B,KAAK,EAAE;gBACL,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,IAAI,EAAE,kBAAkB;aACzB;YACD,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED,IAAI,KAAK,YAAY,sBAAc,EAAE,CAAC;QACpC,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC5B,KAAK,EAAE;gBACL,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,IAAI,EAAE,qBAAqB;aAC5B;YACD,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED,IAAI,KAAK,YAAY,qBAAa,EAAE,CAAC;QACnC,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;YACzC,KAAK,EAAE;gBACL,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,IAAI,EAAE,gBAAgB;aACvB;YACD,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED,IAAI,KAAK,YAAY,oBAAY,EAAE,CAAC;QAClC,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;YACzC,KAAK,EAAE;gBACL,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,IAAI,EAAE,gBAAgB;aACvB;YACD,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED,mCAAmC;IACnC,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,IAAK,KAAa,CAAC,UAAU,EAAE,CAAC;QAC/D,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC5B,KAAK,EAAE;gBACL,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,2BAA2B;gBACpC,OAAO,EAAG,KAAa,CAAC,UAAU;gBAClC,IAAI,EAAE,0BAA0B;aACjC;YACD,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED,wBAAwB;IACxB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;QACvE,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC5B,KAAK,EAAE;gBACL,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE,iBAAiB;aACxB;YACD,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED,wBAAwB;IACxB,eAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE;QAC9B,KAAK,EAAE;YACL,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB;QACD,SAAS;QACT,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,MAAM,EAAE,OAAO,CAAC,MAAM;KACvB,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;QAC5B,KAAK,EAAE;YACL,IAAI,EAAE,gBAAgB;YACtB,OAAO,EAAE,uBAAuB;YAChC,IAAI,EAAE,gBAAgB;SACvB;QACD,SAAS;KACV,CAAC,CAAC;AACL,CAAC,CAAC;AArGW,QAAA,sBAAsB,0BAqGjC;AAEF;;GAEG;AACU,QAAA,cAAc,GAAG;IAC5B,MAAM,EAAE,CAAC,MAAc,EAAE,QAAsD,EAAE,EAAE;QACjF,oEAAoE;QACpE,IAAI,CAAC,MAAM;YAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEzC,6DAA6D;QAC7D,iDAAiD;QACjD,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC9B,CAAC;IACD,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC;IACpD,cAAc,EAAE;QACd,cAAc;QACd,eAAe;QACf,kBAAkB;QAClB,cAAc;QACd,QAAQ;QACR,QAAQ;KACT;IACD,cAAc,EAAE,CAAC,cAAc,CAAC;CACjC,CAAC;AAEF;;GAEG;AACU,QAAA,kBAAkB,GAAG;IAChC,qBAAqB,EAAE;QACrB,UAAU,EAAE;YACV,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;YACvC,SAAS,EAAE,CAAC,QAAQ,CAAC;YACrB,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;YACrC,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,OAAO,EAAE,CAAC,QAAQ,CAAC;YACnB,SAAS,EAAE,CAAC,QAAQ,CAAC;YACrB,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACpB,QAAQ,EAAE,CAAC,QAAQ,CAAC;SACrB;KACF;IACD,yBAAyB,EAAE,KAAK,EAAE,yBAAyB;IAC3D,uBAAuB,EAAE,KAAK,EAAI,yBAAyB;IAC3D,yBAAyB,EAAE,KAAK,EAAE,yBAAyB;IAC3D,kBAAkB,EAAE,IAAI;IACxB,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;IAC9B,aAAa,EAAE,IAAI;IACnB,IAAI,EAAE;QACJ,MAAM,EAAE,QAAQ;QAChB,iBAAiB,EAAE,IAAI;QACvB,OAAO,EAAE,IAAI;KACd;IACD,QAAQ,EAAE,IAAI;IACd,OAAO,EAAE,IAAI;IACb,kBAAkB,EAAE,IAAI;IACxB,4BAA4B,EAAE,KAAK;IACnC,cAAc,EAAE,aAAa;IAC7B,SAAS,EAAE,IAAI;CAChB,CAAC;AAEF;;GAEG;AACU,QAAA,mBAAmB,GAAG;IACjC,GAAG,EAAE,GAAG,EAAE,0BAA0B;IACpC,UAAU,EAAE,UAAU;IACtB,WAAW,EAAE,IAAI;IACjB,sBAAsB,EAAE,KAAK;IAC7B,YAAY,EAAE,CAAC,OAAuB,EAAE,EAAE;QACxC,4BAA4B;QAC5B,OAAO,OAAO,CAAC,EAAE,CAAC;IACpB,CAAC;IACD,oBAAoB,EAAE,CAAC,OAAuB,EAAE,OAAY,EAAE,EAAE;QAC9D,MAAM,SAAS,GAAI,OAAe,CAAC,SAAS,IAAI,SAAS,CAAC;QAE1D,OAAO;YACL,KAAK,EAAE;gBACL,IAAI,EAAE,kBAAkB;gBACxB,OAAO,EAAE,qCAAqC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW;gBACvF,IAAI,EAAE,qBAAqB;gBAC3B,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC;aAC3C;YACD,SAAS;SACV,CAAC;IACJ,CAAC;IACD,UAAU,EAAE;QACV,mBAAmB,EAAE,IAAI;QACzB,uBAAuB,EAAE,IAAI;QAC7B,mBAAmB,EAAE,IAAI;KAC1B;CACF,CAAC;AAEF;;GAEG;AACI,MAAM,qBAAqB,GAAG,KAAK,EAAE,QAAwB,EAAE,KAAmB,EAAE,EAAE;IAC3F,2CAA2C;IAC3C,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACvF,CAAC,CAAC;AAHW,QAAA,qBAAqB,yBAGhC;AAEF;;GAEG;AACU,QAAA,0BAA0B,GAAG;IACxC,SAAS,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,aAAa;CAC3C,CAAC;AAEF;;GAEG;AACU,QAAA,qBAAqB,GAAG;IACnC,MAAM,EAAE,IAAI;IACZ,SAAS,EAAE,IAAI,EAAE,0CAA0C;IAC3D,SAAS,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;CAC/B,CAAC;AAEF;;GAEG;AACI,MAAM,kBAAkB,GAAG,KAAK,EAAE,OAAwB,EAAE,EAAE;IACnE,gCAAgC;IAChC,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,2BAAmB,CAAC,CAAC;IAEnD,6BAA6B;IAC7B,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,gCAAwB,CAAC,CAAC;IAExD,gBAAgB;IAChB,OAAO,CAAC,eAAe,CAAC,8BAAsB,CAAC,CAAC;IAEhD,4CAA4C;IAC5C,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,6BAAqB,CAAC,CAAC;IAC9C,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,6BAAqB,CAAC,CAAC;IAE5C,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;AACxD,CAAC,CAAC;AAfW,QAAA,kBAAkB,sBAe7B;AAEF,kBAAe;IACb,mBAAmB,EAAnB,2BAAmB;IACnB,wBAAwB,EAAxB,gCAAwB;IACxB,sBAAsB,EAAtB,8BAAsB;IACtB,cAAc,EAAd,sBAAc;IACd,kBAAkB,EAAlB,0BAAkB;IAClB,mBAAmB,EAAnB,2BAAmB;IACnB,qBAAqB,EAArB,6BAAqB;IACrB,0BAA0B,EAA1B,kCAA0B;IAC1B,qBAAqB,EAArB,6BAAqB;IACrB,kBAAkB,EAAlB,0BAAkB;CACnB,CAAC"}