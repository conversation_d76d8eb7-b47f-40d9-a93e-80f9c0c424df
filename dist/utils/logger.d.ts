import winston from 'winston';
export declare const logger: winston.Logger;
export declare const accessLogger: winston.Logger;
export declare const errorLogger: winston.Logger;
export declare const providerLogger: winston.Logger;
export declare const metricsLogger: winston.Logger;
export declare const logRequest: (requestId: string, method: string, url: string, userAgent?: string) => void;
export declare const logResponse: (requestId: string, statusCode: number, duration: number, provider?: string, model?: string) => void;
export declare const logError: (error: Error, requestId?: string, provider?: string, model?: string, context?: Record<string, any>) => void;
export declare const logProviderCall: (provider: string, model: string, requestId: string, action: string, duration?: number, success?: boolean, error?: string) => void;
export declare const logMetrics: (metrics: Record<string, any>) => void;
export default logger;
//# sourceMappingURL=logger.d.ts.map