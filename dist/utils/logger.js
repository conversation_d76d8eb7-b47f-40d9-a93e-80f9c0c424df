"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logMetrics = exports.logProviderCall = exports.logError = exports.logResponse = exports.logRequest = exports.metricsLogger = exports.providerLogger = exports.errorLogger = exports.accessLogger = exports.logger = void 0;
const winston_1 = __importDefault(require("winston"));
const winston_daily_rotate_file_1 = __importDefault(require("winston-daily-rotate-file"));
const config_1 = require("@/config");
// Custom log format
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS',
}), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.printf(({ timestamp, level, message, ...meta }) => {
    const logEntry = {
        timestamp,
        level,
        message,
        ...meta,
    };
    // Add request context if available
    if (meta['requestId']) {
        logEntry.requestId = meta['requestId'];
    }
    if (meta['provider']) {
        logEntry.provider = meta['provider'];
    }
    if (meta['model']) {
        logEntry.model = meta['model'];
    }
    return JSON.stringify(logEntry);
}));
// Console format for development
const consoleFormat = winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.timestamp({
    format: 'HH:mm:ss.SSS',
}), winston_1.default.format.printf(({ timestamp, level, message, requestId, provider, model, ...meta }) => {
    let logMessage = `${timestamp} [${level}] ${message}`;
    if (requestId) {
        logMessage += ` [req:${requestId}]`;
    }
    if (provider) {
        logMessage += ` [${provider}]`;
    }
    if (model) {
        logMessage += ` [${model}]`;
    }
    // Add additional metadata if present
    const metaKeys = Object.keys(meta);
    if (metaKeys.length > 0) {
        const metaString = metaKeys
            .map(key => `${key}:${meta[key]}`)
            .join(' ');
        logMessage += ` {${metaString}}`;
    }
    return logMessage;
}));
// Create transports
const transports = [];
// Console transport for development
if (config_1.ENV.NODE_ENV === 'development') {
    transports.push(new winston_1.default.transports.Console({
        format: consoleFormat,
        level: config_1.ENV.LOG_LEVEL,
    }));
}
else {
    // JSON console output for production
    transports.push(new winston_1.default.transports.Console({
        format: logFormat,
        level: config_1.ENV.LOG_LEVEL,
    }));
}
// File transports for production
if (config_1.ENV.NODE_ENV === 'production') {
    // Error logs
    transports.push(new winston_daily_rotate_file_1.default({
        filename: 'logs/error-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        level: 'error',
        format: logFormat,
        maxSize: '20m',
        maxFiles: '14d',
        zippedArchive: true,
    }));
    // Combined logs
    transports.push(new winston_daily_rotate_file_1.default({
        filename: 'logs/combined-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        format: logFormat,
        maxSize: '20m',
        maxFiles: '7d',
        zippedArchive: true,
    }));
    // Access logs
    transports.push(new winston_daily_rotate_file_1.default({
        filename: 'logs/access-%DATE%.log',
        datePattern: 'YYYY-MM-DD',
        level: 'info',
        format: logFormat,
        maxSize: '20m',
        maxFiles: '7d',
        zippedArchive: true,
    }));
}
// Create logger instance
exports.logger = winston_1.default.createLogger({
    level: config_1.ENV.LOG_LEVEL,
    format: logFormat,
    transports,
    exitOnError: false,
    // Handle uncaught exceptions and rejections
    exceptionHandlers: [
        new winston_1.default.transports.File({ filename: 'logs/exceptions.log' }),
    ],
    rejectionHandlers: [
        new winston_1.default.transports.File({ filename: 'logs/rejections.log' }),
    ],
});
// Create specialized loggers
exports.accessLogger = exports.logger.child({ component: 'access' });
exports.errorLogger = exports.logger.child({ component: 'error' });
exports.providerLogger = exports.logger.child({ component: 'provider' });
exports.metricsLogger = exports.logger.child({ component: 'metrics' });
// Helper functions for structured logging
const logRequest = (requestId, method, url, userAgent) => {
    exports.accessLogger.info('Request received', {
        requestId,
        method,
        url,
        userAgent,
    });
};
exports.logRequest = logRequest;
const logResponse = (requestId, statusCode, duration, provider, model) => {
    exports.accessLogger.info('Request completed', {
        requestId,
        statusCode,
        duration,
        provider,
        model,
    });
};
exports.logResponse = logResponse;
const logError = (error, requestId, provider, model, context) => {
    exports.errorLogger.error('Error occurred', {
        error: {
            name: error.name,
            message: error.message,
            stack: error.stack,
        },
        requestId,
        provider,
        model,
        ...context,
    });
};
exports.logError = logError;
const logProviderCall = (provider, model, requestId, action, duration, success, error) => {
    exports.providerLogger.info('Provider call', {
        provider,
        model,
        requestId,
        action,
        duration,
        success,
        error,
    });
};
exports.logProviderCall = logProviderCall;
const logMetrics = (metrics) => {
    exports.metricsLogger.info('Metrics', metrics);
};
exports.logMetrics = logMetrics;
// Export default logger
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map