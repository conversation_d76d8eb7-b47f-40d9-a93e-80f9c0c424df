"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.throttle = exports.debounce = exports.safeJsonParse = exports.truncateString = exports.isEmpty = exports.deepClone = exports.getUptime = exports.getMemoryUsage = exports.formatBytes = exports.extractProviderFromModel = exports.sanitizeErrorMessage = exports.validateChatCompletionRequest = exports.withTimeout = exports.retryWithBackoff = exports.sleep = exports.calculateDuration = exports.getPerformanceTimestamp = exports.getCurrentTimestamp = exports.generateRequestId = void 0;
const uuid_1 = require("uuid");
const perf_hooks_1 = require("perf_hooks");
const p_retry_1 = __importDefault(require("p-retry"));
const p_timeout_1 = __importDefault(require("p-timeout"));
const types_1 = require("@/types");
const config_1 = require("@/config");
/**
 * Generate a unique request ID
 */
const generateRequestId = () => {
    return (0, uuid_1.v4)();
};
exports.generateRequestId = generateRequestId;
/**
 * Get current timestamp in milliseconds
 */
const getCurrentTimestamp = () => {
    return Date.now();
};
exports.getCurrentTimestamp = getCurrentTimestamp;
/**
 * Get high-resolution timestamp for performance measurement
 */
const getPerformanceTimestamp = () => {
    return perf_hooks_1.performance.now();
};
exports.getPerformanceTimestamp = getPerformanceTimestamp;
/**
 * Calculate duration between two performance timestamps
 */
const calculateDuration = (startTime, endTime) => {
    const end = endTime || perf_hooks_1.performance.now();
    return Math.round((end - startTime) * 100) / 100; // Round to 2 decimal places
};
exports.calculateDuration = calculateDuration;
/**
 * Sleep for specified milliseconds
 */
const sleep = (ms) => {
    return new Promise(resolve => setTimeout(resolve, ms));
};
exports.sleep = sleep;
/**
 * Retry function with exponential backoff
 */
const retryWithBackoff = async (fn, options = {}) => {
    const { retries = config_1.ENV.MAX_RETRIES, minTimeout = config_1.ENV.RETRY_DELAY, maxTimeout = 30000, factor = 2, onFailedAttempt, } = options;
    return (0, p_retry_1.default)(fn, {
        retries,
        minTimeout,
        maxTimeout,
        factor,
        onFailedAttempt: (error) => {
            if (onFailedAttempt) {
                onFailedAttempt(error, error.attemptNumber);
            }
        },
    });
};
exports.retryWithBackoff = retryWithBackoff;
/**
 * Add timeout to a promise
 */
const withTimeout = async (promise, timeoutMs = config_1.ENV.REQUEST_TIMEOUT, errorMessage = 'Operation timed out') => {
    return (0, p_timeout_1.default)(promise, {
        milliseconds: timeoutMs,
        message: errorMessage,
    });
};
exports.withTimeout = withTimeout;
/**
 * Validate chat completion request
 */
const validateChatCompletionRequest = (request) => {
    if (!request.model) {
        throw new types_1.ValidationError('Model is required');
    }
    if (!request.messages || !Array.isArray(request.messages) || request.messages.length === 0) {
        throw new types_1.ValidationError('Messages array is required and must not be empty');
    }
    // Validate messages
    for (const [index, message] of request.messages.entries()) {
        if (!message.role || !['system', 'user', 'assistant', 'tool'].includes(message.role)) {
            throw new types_1.ValidationError(`Invalid role at message index ${index}`);
        }
        if (!message.content || typeof message.content !== 'string') {
            throw new types_1.ValidationError(`Invalid content at message index ${index}`);
        }
    }
    // Validate optional parameters
    if (request.temperature !== undefined) {
        if (typeof request.temperature !== 'number' || request.temperature < 0 || request.temperature > 2) {
            throw new types_1.ValidationError('Temperature must be a number between 0 and 2');
        }
    }
    if (request.maxTokens !== undefined) {
        if (typeof request.maxTokens !== 'number' || request.maxTokens < 1) {
            throw new types_1.ValidationError('Max tokens must be a positive number');
        }
    }
    if (request.topP !== undefined) {
        if (typeof request.topP !== 'number' || request.topP < 0 || request.topP > 1) {
            throw new types_1.ValidationError('Top P must be a number between 0 and 1');
        }
    }
    if (request.topK !== undefined) {
        if (typeof request.topK !== 'number' || request.topK < 1) {
            throw new types_1.ValidationError('Top K must be a positive number');
        }
    }
};
exports.validateChatCompletionRequest = validateChatCompletionRequest;
/**
 * Sanitize error message for client response
 */
const sanitizeErrorMessage = (error, includeStack = false) => {
    const sanitized = {
        name: error.name,
        message: error.message,
    };
    if (includeStack && config_1.ENV.NODE_ENV === 'development') {
        sanitized.stack = error.stack;
    }
    return sanitized;
};
exports.sanitizeErrorMessage = sanitizeErrorMessage;
/**
 * Extract provider from model name
 */
const extractProviderFromModel = (modelName) => {
    const lowerModel = modelName.toLowerCase();
    if (lowerModel.includes('gpt') || lowerModel.includes('openai')) {
        return 'openai';
    }
    if (lowerModel.includes('gemini') || lowerModel.includes('google')) {
        return 'gemini';
    }
    return null;
};
exports.extractProviderFromModel = extractProviderFromModel;
/**
 * Format bytes to human readable string
 */
const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};
exports.formatBytes = formatBytes;
/**
 * Get memory usage information
 */
const getMemoryUsage = () => {
    const usage = process.memoryUsage();
    return {
        rss: (0, exports.formatBytes)(usage.rss),
        heapTotal: (0, exports.formatBytes)(usage.heapTotal),
        heapUsed: (0, exports.formatBytes)(usage.heapUsed),
        external: (0, exports.formatBytes)(usage.external),
        arrayBuffers: (0, exports.formatBytes)(usage.arrayBuffers),
    };
};
exports.getMemoryUsage = getMemoryUsage;
/**
 * Get system uptime in seconds
 */
const getUptime = () => {
    return Math.floor(process.uptime());
};
exports.getUptime = getUptime;
/**
 * Deep clone an object
 */
const deepClone = (obj) => {
    return JSON.parse(JSON.stringify(obj));
};
exports.deepClone = deepClone;
/**
 * Check if a value is empty (null, undefined, empty string, empty array, empty object)
 */
const isEmpty = (value) => {
    if (value == null)
        return true;
    if (typeof value === 'string')
        return value.trim().length === 0;
    if (Array.isArray(value))
        return value.length === 0;
    if (typeof value === 'object')
        return Object.keys(value).length === 0;
    return false;
};
exports.isEmpty = isEmpty;
/**
 * Truncate string to specified length
 */
const truncateString = (str, maxLength, suffix = '...') => {
    if (str.length <= maxLength)
        return str;
    return str.substring(0, maxLength - suffix.length) + suffix;
};
exports.truncateString = truncateString;
/**
 * Parse JSON safely
 */
const safeJsonParse = (jsonString, defaultValue = null) => {
    try {
        return JSON.parse(jsonString);
    }
    catch {
        return defaultValue;
    }
};
exports.safeJsonParse = safeJsonParse;
/**
 * Create a debounced function
 */
const debounce = (func, wait) => {
    let timeout;
    return (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
};
exports.debounce = debounce;
/**
 * Create a throttled function
 */
const throttle = (func, limit) => {
    let inThrottle;
    return (...args) => {
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};
exports.throttle = throttle;
//# sourceMappingURL=index.js.map