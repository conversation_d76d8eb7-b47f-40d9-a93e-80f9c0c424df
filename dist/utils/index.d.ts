import { ChatCompletionRequest, AIProvider } from '../types';
/**
 * Generate a unique request ID
 */
export declare const generateRequestId: () => string;
/**
 * Get current timestamp in milliseconds
 */
export declare const getCurrentTimestamp: () => number;
/**
 * Get high-resolution timestamp for performance measurement
 */
export declare const getPerformanceTimestamp: () => number;
/**
 * Calculate duration between two performance timestamps
 */
export declare const calculateDuration: (startTime: number, endTime?: number) => number;
/**
 * Sleep for specified milliseconds
 */
export declare const sleep: (ms: number) => Promise<void>;
/**
 * Retry function with exponential backoff
 */
export declare const retryWithBackoff: <T>(fn: () => Promise<T>, options?: {
    retries?: number;
    minTimeout?: number;
    maxTimeout?: number;
    factor?: number;
    onFailedAttempt?: (error: any, attemptNumber: number) => void;
}) => Promise<T>;
/**
 * Add timeout to a promise
 */
export declare const withTimeout: <T>(promise: Promise<T>, timeoutMs?: number, errorMessage?: string) => Promise<T>;
/**
 * Validate chat completion request
 */
export declare const validateChatCompletionRequest: (request: ChatCompletionRequest) => void;
/**
 * Sanitize error message for client response
 */
export declare const sanitizeErrorMessage: (error: Error, includeStack?: boolean) => any;
/**
 * Extract provider from model name
 */
export declare const extractProviderFromModel: (modelName: string) => AIProvider | null;
/**
 * Format bytes to human readable string
 */
export declare const formatBytes: (bytes: number, decimals?: number) => string;
/**
 * Get memory usage information
 */
export declare const getMemoryUsage: () => {
    rss: string;
    heapTotal: string;
    heapUsed: string;
    external: string;
    arrayBuffers: string;
};
/**
 * Get system uptime in seconds
 */
export declare const getUptime: () => number;
/**
 * Deep clone an object
 */
export declare const deepClone: <T>(obj: T) => T;
/**
 * Check if a value is empty (null, undefined, empty string, empty array, empty object)
 */
export declare const isEmpty: (value: any) => boolean;
/**
 * Truncate string to specified length
 */
export declare const truncateString: (str: string, maxLength: number, suffix?: string) => string;
/**
 * Parse JSON safely
 */
export declare const safeJsonParse: <T = any>(jsonString: string, defaultValue?: T | null) => T | null;
/**
 * Create a debounced function
 */
export declare const debounce: <T extends (...args: any[]) => any>(func: T, wait: number) => ((...args: Parameters<T>) => void);
/**
 * Create a throttled function
 */
export declare const throttle: <T extends (...args: any[]) => any>(func: T, limit: number) => ((...args: Parameters<T>) => void);
//# sourceMappingURL=index.d.ts.map