"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshHealth = exports.getMetrics = exports.healthCheck = exports.getModel = exports.getModels = exports.chatCompletions = exports.chatCompletionSchema = void 0;
const ai_1 = require("@/services/ai");
const logger_1 = require("@/utils/logger");
// Request schema for validation
exports.chatCompletionSchema = {
    body: {
        type: 'object',
        required: ['model', 'messages'],
        properties: {
            model: {
                type: 'string',
                description: 'ID of the model to use',
            },
            messages: {
                type: 'array',
                items: {
                    type: 'object',
                    required: ['role', 'content'],
                    properties: {
                        role: {
                            type: 'string',
                            enum: ['system', 'user', 'assistant', 'tool'],
                        },
                        content: {
                            type: 'string',
                        },
                        name: {
                            type: 'string',
                        },
                        toolCallId: {
                            type: 'string',
                        },
                    },
                },
                minItems: 1,
            },
            stream: {
                type: 'boolean',
                default: false,
            },
            temperature: {
                type: 'number',
                minimum: 0,
                maximum: 2,
            },
            maxTokens: {
                type: 'integer',
                minimum: 1,
            },
            topP: {
                type: 'number',
                minimum: 0,
                maximum: 1,
            },
            topK: {
                type: 'integer',
                minimum: 1,
            },
            stop: {
                oneOf: [
                    { type: 'string' },
                    {
                        type: 'array',
                        items: { type: 'string' },
                        maxItems: 4,
                    },
                ],
            },
            presencePenalty: {
                type: 'number',
                minimum: -2,
                maximum: 2,
            },
            frequencyPenalty: {
                type: 'number',
                minimum: -2,
                maximum: 2,
            },
            user: {
                type: 'string',
            },
            tools: {
                type: 'array',
                items: {
                    type: 'object',
                },
            },
            toolChoice: {
                oneOf: [
                    { type: 'string' },
                    { type: 'object' },
                ],
            },
        },
    },
};
/**
 * Handle chat completion requests (both streaming and non-streaming)
 */
const chatCompletions = async (request, reply) => {
    const requestId = request.requestId;
    const { stream = false, ...chatRequest } = request.body;
    try {
        if (stream) {
            // Handle streaming response
            return await handleStreamingResponse(chatRequest, reply, requestId);
        }
        else {
            // Handle regular response
            return await handleRegularResponse(chatRequest, reply, requestId);
        }
    }
    catch (error) {
        // Error handling is done by the error middleware
        throw error;
    }
};
exports.chatCompletions = chatCompletions;
/**
 * Handle regular (non-streaming) chat completion
 */
const handleRegularResponse = async (request, reply, requestId) => {
    const response = await ai_1.aiService.chatCompletion(request, requestId);
    return reply
        .status(200)
        .header('content-type', 'application/json')
        .send(response);
};
/**
 * Handle streaming chat completion using SSE
 */
const handleStreamingResponse = async (request, reply, requestId) => {
    // Set SSE headers
    reply
        .status(200)
        .header('content-type', 'text/event-stream')
        .header('cache-control', 'no-cache')
        .header('connection', 'keep-alive')
        .header('access-control-allow-origin', '*')
        .header('access-control-allow-headers', 'Cache-Control');
    // Handle client disconnect
    let isClientConnected = true;
    request.raw.on('close', () => {
        isClientConnected = false;
        logger_1.logger.info('Client disconnected from stream', { requestId });
    });
    request.raw.on('error', (error) => {
        isClientConnected = false;
        logger_1.logger.warn('Stream error', { requestId, error: error.message });
    });
    try {
        const stream = ai_1.aiService.chatCompletionStream(request, requestId);
        for await (const chunk of stream) {
            // Check if client is still connected
            if (!isClientConnected) {
                logger_1.logger.info('Stopping stream due to client disconnect', { requestId });
                break;
            }
            // Send SSE data
            const sseData = `data: ${JSON.stringify(chunk)}\n\n`;
            reply.raw.write(sseData);
            // Check if this is the final chunk
            if (chunk.choices[0]?.finishReason) {
                break;
            }
        }
        // Send final SSE message
        if (isClientConnected) {
            reply.raw.write('data: [DONE]\n\n');
        }
    }
    catch (error) {
        // Send error as SSE
        if (isClientConnected) {
            const errorData = {
                error: {
                    type: 'stream_error',
                    message: error.message,
                },
            };
            reply.raw.write(`data: ${JSON.stringify(errorData)}\n\n`);
            reply.raw.write('data: [DONE]\n\n');
        }
        throw error;
    }
    finally {
        // End the response
        if (isClientConnected) {
            reply.raw.end();
        }
    }
};
/**
 * Get available models
 */
const getModels = async (_request, reply) => {
    const models = ai_1.aiService.getAvailableModels();
    // Transform to OpenAI-compatible format
    const openaiFormat = {
        object: 'list',
        data: models.map(model => ({
            id: model.id,
            object: 'model',
            created: Math.floor(Date.now() / 1000),
            owned_by: model.provider,
            permission: [],
            root: model.id,
            parent: null,
            // Additional metadata
            provider: model.provider,
            name: model.name,
            enabled: model.enabled,
            maxTokens: model.maxTokens,
            temperature: model.temperature,
            fallbackModels: model.fallbackModels,
            rateLimits: model.rateLimits,
            providerHealthy: model.providerHealthy,
            lastHealthCheck: model.lastHealthCheck,
        })),
    };
    return reply.status(200).send(openaiFormat);
};
exports.getModels = getModels;
/**
 * Get specific model information
 */
const getModel = async (request, reply) => {
    const { model } = request.params;
    const modelInfo = ai_1.aiService.getModelInfo(model);
    if (!modelInfo) {
        return reply.status(404).send({
            error: {
                type: 'not_found',
                message: `Model '${model}' not found`,
                code: 'MODEL_NOT_FOUND',
            },
        });
    }
    // Transform to OpenAI-compatible format
    const openaiFormat = {
        id: modelInfo.id,
        object: 'model',
        created: Math.floor(Date.now() / 1000),
        owned_by: modelInfo.provider,
        permission: [],
        root: modelInfo.id,
        parent: null,
        // Additional metadata
        provider: modelInfo.provider,
        name: modelInfo.name,
        enabled: modelInfo.enabled,
        maxTokens: modelInfo.maxTokens,
        temperature: modelInfo.temperature,
        fallbackModels: modelInfo.fallbackModels,
        rateLimits: modelInfo.rateLimits,
        providerHealthy: modelInfo.providerHealthy,
        lastHealthCheck: modelInfo.lastHealthCheck,
    };
    return reply.status(200).send(openaiFormat);
};
exports.getModel = getModel;
/**
 * Health check endpoint
 */
const healthCheck = async (_request, reply) => {
    const health = ai_1.aiService.getHealthStatus();
    const isHealthy = Object.values(health.health).every(provider => provider.healthy);
    const response = {
        status: isHealthy ? 'healthy' : 'degraded',
        timestamp: new Date().toISOString(),
        version: process.env['npm_package_version'] || '1.0.0',
        uptime: process.uptime(),
        ...health,
    };
    return reply.status(isHealthy ? 200 : 503).send(response);
};
exports.healthCheck = healthCheck;
/**
 * Metrics endpoint
 */
const getMetrics = async (_request, reply) => {
    const metrics = ai_1.aiService.getMetrics();
    return reply.status(200).send({
        timestamp: new Date().toISOString(),
        ...metrics,
    });
};
exports.getMetrics = getMetrics;
/**
 * Refresh health status
 */
const refreshHealth = async (_request, reply) => {
    await ai_1.aiService.refreshHealth();
    return reply.status(200).send({
        message: 'Health status refreshed',
        timestamp: new Date().toISOString(),
    });
};
exports.refreshHealth = refreshHealth;
//# sourceMappingURL=chat.js.map