import { FastifyRequest, FastifyReply } from 'fastify';
import { ChatCompletionRequest, TypedFastifyRequest, TypedFastifyReply } from '../types';
export declare const chatCompletionSchema: {
    body: {
        type: string;
        required: string[];
        properties: {
            model: {
                type: string;
                description: string;
            };
            messages: {
                type: string;
                items: {
                    type: string;
                    required: string[];
                    properties: {
                        role: {
                            type: string;
                            enum: string[];
                        };
                        content: {
                            type: string;
                        };
                        name: {
                            type: string;
                        };
                        toolCallId: {
                            type: string;
                        };
                    };
                };
                minItems: number;
            };
            stream: {
                type: string;
                default: boolean;
            };
            temperature: {
                type: string;
                minimum: number;
                maximum: number;
            };
            maxTokens: {
                type: string;
                minimum: number;
            };
            topP: {
                type: string;
                minimum: number;
                maximum: number;
            };
            topK: {
                type: string;
                minimum: number;
            };
            stop: {
                oneOf: ({
                    type: string;
                    items?: never;
                    maxItems?: never;
                } | {
                    type: string;
                    items: {
                        type: string;
                    };
                    maxItems: number;
                })[];
            };
            presencePenalty: {
                type: string;
                minimum: number;
                maximum: number;
            };
            frequencyPenalty: {
                type: string;
                minimum: number;
                maximum: number;
            };
            user: {
                type: string;
            };
            tools: {
                type: string;
                items: {
                    type: string;
                };
            };
            toolChoice: {
                oneOf: {
                    type: string;
                }[];
            };
        };
    };
};
/**
 * Handle chat completion requests (both streaming and non-streaming)
 */
export declare const chatCompletions: (request: TypedFastifyRequest<ChatCompletionRequest>, reply: TypedFastifyReply) => Promise<void>;
/**
 * Get available models
 */
export declare const getModels: (_request: FastifyRequest, reply: FastifyReply) => Promise<never>;
/**
 * Get specific model information
 */
export declare const getModel: (request: FastifyRequest<{
    Params: {
        model: string;
    };
}>, reply: FastifyReply) => Promise<never>;
/**
 * Health check endpoint
 */
export declare const healthCheck: (_request: FastifyRequest, reply: FastifyReply) => Promise<never>;
/**
 * Metrics endpoint
 */
export declare const getMetrics: (_request: FastifyRequest, reply: FastifyReply) => Promise<never>;
/**
 * Refresh health status
 */
export declare const refreshHealth: (_request: FastifyRequest, reply: FastifyReply) => Promise<never>;
//# sourceMappingURL=chat.d.ts.map