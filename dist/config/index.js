"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ENV = exports.serviceConfig = void 0;
const dotenv_1 = require("dotenv");
const joi_1 = __importDefault(require("joi"));
// Load environment variables
(0, dotenv_1.config)();
// Environment validation schema
const envSchema = joi_1.default.object({
    NODE_ENV: joi_1.default.string().valid('development', 'production', 'test').default('development'),
    PORT: joi_1.default.number().port().default(3000),
    HOST: joi_1.default.string().default('0.0.0.0'),
    LOG_LEVEL: joi_1.default.string().valid('error', 'warn', 'info', 'debug').default('info'),
    // API Keys
    OPENAI_API_KEY: joi_1.default.string().required(),
    GEMINI_API_KEY: joi_1.default.string().required(),
    // Google Cloud
    GOOGLE_CLOUD_PROJECT: joi_1.default.string().optional(),
    GOOGLE_CLOUD_LOCATION: joi_1.default.string().default('us-central1'),
    // Rate Limiting
    RATE_LIMIT_MAX: joi_1.default.number().positive().default(100),
    RATE_LIMIT_WINDOW: joi_1.default.number().positive().default(60000),
    // Security
    CORS_ORIGIN: joi_1.default.string().default('*'),
    HELMET_ENABLED: joi_1.default.boolean().default(true),
    // Monitoring
    HEALTH_CHECK_ENABLED: joi_1.default.boolean().default(true),
    METRICS_ENABLED: joi_1.default.boolean().default(true),
    // Models
    DEFAULT_MODEL_PROVIDER: joi_1.default.string().valid('openai', 'gemini').default('openai'),
    DEFAULT_OPENAI_MODEL: joi_1.default.string().default('gpt-4o'),
    DEFAULT_GEMINI_MODEL: joi_1.default.string().default('gemini-2.0-flash'),
    // Timeouts
    REQUEST_TIMEOUT: joi_1.default.number().positive().default(30000),
    STREAM_TIMEOUT: joi_1.default.number().positive().default(60000),
    // Retry
    MAX_RETRIES: joi_1.default.number().min(0).max(10).default(3),
    RETRY_DELAY: joi_1.default.number().positive().default(1000),
    // Load Balancing
    ENABLE_LOAD_BALANCING: joi_1.default.boolean().default(true),
    FALLBACK_ENABLED: joi_1.default.boolean().default(true),
}).unknown();
const { error, value: env } = envSchema.validate(process.env);
if (error) {
    throw new Error(`Config validation error: ${error.message}`);
}
// Default model configurations
const defaultModels = [
    // OpenAI Models
    {
        id: 'gpt-4o',
        name: 'GPT-4o',
        provider: 'openai',
        authMethod: 'bearer',
        maxTokens: 4096,
        temperature: 0.7,
        enabled: true,
        fallbackModels: ['gpt-4o-mini', 'gpt-3.5-turbo'],
        rateLimits: {
            requestsPerMinute: 500,
            tokensPerMinute: 150000,
        },
    },
    {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        provider: 'openai',
        authMethod: 'bearer',
        maxTokens: 16384,
        temperature: 0.7,
        enabled: true,
        fallbackModels: ['gpt-3.5-turbo'],
        rateLimits: {
            requestsPerMinute: 1000,
            tokensPerMinute: 200000,
        },
    },
    {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        provider: 'openai',
        authMethod: 'bearer',
        maxTokens: 4096,
        temperature: 0.7,
        enabled: true,
        rateLimits: {
            requestsPerMinute: 3500,
            tokensPerMinute: 90000,
        },
    },
    // Gemini Models
    {
        id: 'gemini-2.0-flash',
        name: 'Gemini 2.0 Flash',
        provider: 'gemini',
        authMethod: 'api-key',
        maxTokens: 8192,
        temperature: 0.7,
        enabled: true,
        fallbackModels: ['gemini-1.5-pro', 'gemini-1.5-flash'],
        rateLimits: {
            requestsPerMinute: 1000,
            tokensPerMinute: 1000000,
        },
    },
    {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        provider: 'gemini',
        authMethod: 'api-key',
        maxTokens: 8192,
        temperature: 0.7,
        enabled: true,
        fallbackModels: ['gemini-1.5-flash'],
        rateLimits: {
            requestsPerMinute: 360,
            tokensPerMinute: 1000000,
        },
    },
    {
        id: 'gemini-1.5-flash',
        name: 'Gemini 1.5 Flash',
        provider: 'gemini',
        authMethod: 'api-key',
        maxTokens: 8192,
        temperature: 0.7,
        enabled: true,
        rateLimits: {
            requestsPerMinute: 1000,
            tokensPerMinute: 1000000,
        },
    },
];
// Build service configuration
exports.serviceConfig = {
    server: {
        port: env.PORT,
        host: env.HOST,
        logLevel: env.LOG_LEVEL,
    },
    providers: {
        openai: {
            apiKey: env.OPENAI_API_KEY,
            timeout: env.REQUEST_TIMEOUT,
            maxRetries: env.MAX_RETRIES,
        },
        gemini: {
            apiKey: env.GEMINI_API_KEY,
            project: env.GOOGLE_CLOUD_PROJECT,
            location: env.GOOGLE_CLOUD_LOCATION,
            authMethod: 'api-key',
            timeout: env.REQUEST_TIMEOUT,
            maxRetries: env.MAX_RETRIES,
        },
    },
    models: defaultModels,
    rateLimit: {
        max: env.RATE_LIMIT_MAX,
        timeWindow: env.RATE_LIMIT_WINDOW,
    },
    security: {
        corsOrigin: env.CORS_ORIGIN,
        helmetEnabled: env.HELMET_ENABLED,
    },
    monitoring: {
        healthCheckEnabled: env.HEALTH_CHECK_ENABLED,
        metricsEnabled: env.METRICS_ENABLED,
    },
};
// Export environment variables for direct access
exports.ENV = {
    NODE_ENV: env.NODE_ENV,
    PORT: env.PORT,
    HOST: env.HOST,
    LOG_LEVEL: env.LOG_LEVEL,
    REQUEST_TIMEOUT: env.REQUEST_TIMEOUT,
    STREAM_TIMEOUT: env.STREAM_TIMEOUT,
    MAX_RETRIES: env.MAX_RETRIES,
    RETRY_DELAY: env.RETRY_DELAY,
    ENABLE_LOAD_BALANCING: env.ENABLE_LOAD_BALANCING,
    FALLBACK_ENABLED: env.FALLBACK_ENABLED,
};
exports.default = exports.serviceConfig;
//# sourceMappingURL=index.js.map