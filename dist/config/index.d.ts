import { ServiceConfig } from '../types';
export declare const serviceConfig: ServiceConfig;
export declare const ENV: {
    readonly NODE_ENV: any;
    readonly PORT: any;
    readonly HOST: any;
    readonly LOG_LEVEL: any;
    readonly REQUEST_TIMEOUT: any;
    readonly STREAM_TIMEOUT: any;
    readonly MAX_RETRIES: any;
    readonly RETRY_DELAY: any;
    readonly ENABLE_LOAD_BALANCING: any;
    readonly FALLBACK_ENABLED: any;
};
export default serviceConfig;
//# sourceMappingURL=index.d.ts.map