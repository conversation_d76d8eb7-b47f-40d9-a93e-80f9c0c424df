import { FastifyInstance } from 'fastify';
/**
 * Build Fastify application with all plugins and routes
 */
export declare const buildApp: () => Promise<FastifyInstance>;
/**
 * Start the server
 */
export declare const startServer: () => Promise<void>;
declare const _default: {
    buildApp: () => Promise<FastifyInstance>;
    startServer: () => Promise<void>;
};
export default _default;
//# sourceMappingURL=app.d.ts.map