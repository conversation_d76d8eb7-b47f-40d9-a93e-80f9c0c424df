import { FastifyRequest, FastifyReply } from 'fastify';
export type AIProvider = 'openai' | 'gemini';
export type AuthMethod = 'bearer' | 'google-auth' | 'api-key';
export interface ModelConfig {
    id: string;
    name: string;
    provider: AIProvider;
    endpoint?: string;
    authMethod: AuthMethod;
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    topK?: number;
    enabled: boolean;
    fallbackModels?: string[];
    rateLimits?: {
        requestsPerMinute: number;
        tokensPerMinute: number;
    };
}
export interface ChatMessage {
    role: 'system' | 'user' | 'assistant' | 'tool';
    content: string;
    name?: string;
    toolCallId?: string;
}
export interface ChatCompletionRequest {
    model: string;
    messages: ChatMessage[];
    stream?: boolean;
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    topK?: number;
    stop?: string | string[];
    presencePenalty?: number;
    frequencyPenalty?: number;
    user?: string;
    tools?: any[];
    toolChoice?: any;
}
export interface ChatCompletionResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
        index: number;
        message: ChatMessage;
        finishReason: string | null;
    }>;
    usage: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    provider: AIProvider;
    processingTime: number;
}
export interface StreamChunk {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
        index: number;
        delta: Partial<ChatMessage>;
        finishReason: string | null;
    }>;
    provider: AIProvider;
}
export interface AIProviderAdapter {
    provider: AIProvider;
    isHealthy(): Promise<boolean>;
    chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
    chatCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk>;
    validateRequest(request: ChatCompletionRequest): void;
}
export interface ServiceConfig {
    server: {
        port: number;
        host: string;
        logLevel: string;
    };
    providers: {
        openai: {
            apiKey: string;
            baseURL?: string;
            timeout: number;
            maxRetries: number;
        };
        gemini: {
            apiKey: string;
            project?: string;
            location?: string;
            authMethod: AuthMethod;
            timeout: number;
            maxRetries: number;
        };
    };
    models: ModelConfig[];
    rateLimit: {
        max: number;
        timeWindow: number;
    };
    security: {
        corsOrigin: string;
        helmetEnabled: boolean;
    };
    monitoring: {
        healthCheckEnabled: boolean;
        metricsEnabled: boolean;
    };
}
export declare class AIProxyError extends Error {
    statusCode: number;
    provider?: AIProvider | undefined;
    originalError?: Error | undefined;
    constructor(message: string, statusCode?: number, provider?: AIProvider | undefined, originalError?: Error | undefined);
}
export declare class ValidationError extends AIProxyError {
    constructor(message: string, _field?: string);
}
export declare class ProviderError extends AIProxyError {
    constructor(message: string, provider: AIProvider, originalError?: Error);
}
export declare class RateLimitError extends AIProxyError {
    constructor(message?: string);
}
declare module 'fastify' {
    interface FastifyInstance {
        config: ServiceConfig;
    }
}
export interface TypedFastifyRequest<T = any> extends FastifyRequest {
    body: T;
}
export interface TypedFastifyReply extends FastifyReply {
    sse: (data: any) => void;
}
export interface RequestMetrics {
    requestId: string;
    provider: AIProvider;
    model: string;
    startTime: number;
    endTime?: number;
    duration?: number;
    tokenUsage?: {
        prompt: number;
        completion: number;
        total: number;
    };
    success: boolean;
    error?: string;
}
export interface HealthStatus {
    status: 'healthy' | 'unhealthy' | 'degraded';
    timestamp: string;
    version: string;
    uptime: number;
    providers: Record<AIProvider, {
        status: 'healthy' | 'unhealthy';
        latency?: number;
        lastCheck: string;
    }>;
    system: {
        memory: {
            used: number;
            total: number;
            percentage: number;
        };
        cpu: {
            usage: number;
        };
    };
}
//# sourceMappingURL=index.d.ts.map