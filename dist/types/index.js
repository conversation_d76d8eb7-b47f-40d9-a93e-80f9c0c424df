"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitError = exports.ProviderError = exports.ValidationError = exports.AIProxyError = void 0;
// Error Types
class AIProxyError extends Error {
    statusCode;
    provider;
    originalError;
    constructor(message, statusCode = 500, provider, originalError) {
        super(message);
        this.statusCode = statusCode;
        this.provider = provider;
        this.originalError = originalError;
        this.name = 'AIProxyError';
    }
}
exports.AIProxyError = AIProxyError;
class ValidationError extends AIProxyError {
    constructor(message, _field) {
        super(message, 400);
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
class ProviderError extends AIProxyError {
    constructor(message, provider, originalError) {
        super(message, 502, provider, originalError);
        this.name = 'ProviderError';
    }
}
exports.ProviderError = ProviderError;
class RateLimitError extends AIProxyError {
    constructor(message = 'Rate limit exceeded') {
        super(message, 429);
        this.name = 'RateLimitError';
    }
}
exports.RateLimitError = RateLimitError;
//# sourceMappingURL=index.js.map