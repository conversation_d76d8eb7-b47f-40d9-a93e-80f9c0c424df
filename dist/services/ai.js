"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.aiService = exports.AIService = void 0;
const types_1 = require("@/types");
const adapters_1 = require("@/adapters");
const logger_1 = require("@/utils/logger");
const utils_1 = require("@/utils");
class AIService {
    metrics = new Map();
    maxMetricsHistory = 1000;
    /**
     * Process chat completion request
     */
    async chatCompletion(request, requestId) {
        const reqId = requestId || (0, utils_1.generateRequestId)();
        const startTime = (0, utils_1.getPerformanceTimestamp)();
        try {
            // Validate request
            adapters_1.adapterManager.validateRequest(request);
            // Log request
            (0, logger_1.logRequest)(reqId, 'POST', '/v1/chat/completions');
            // Get model configuration
            const modelConfig = adapters_1.adapterManager.getModelConfig(request.model);
            if (!modelConfig) {
                throw new types_1.ValidationError(`Model '${request.model}' not found`);
            }
            logger_1.logger.info('Processing chat completion request', {
                requestId: reqId,
                model: request.model,
                provider: modelConfig.provider,
                messageCount: request.messages.length,
                stream: false,
            });
            // Process request through adapter manager
            const response = await adapters_1.adapterManager.chatCompletion(request);
            const duration = (0, utils_1.calculateDuration)(startTime);
            // Record metrics
            this.recordMetrics({
                requestId: reqId,
                provider: response.provider,
                model: response.model,
                startTime: startTime,
                endTime: (0, utils_1.getPerformanceTimestamp)(),
                duration,
                tokenUsage: {
                    prompt: response.usage.promptTokens,
                    completion: response.usage.completionTokens,
                    total: response.usage.totalTokens,
                },
                success: true,
            });
            // Log response
            (0, logger_1.logResponse)(reqId, 200, duration, response.provider, response.model);
            logger_1.logger.info('Chat completion request completed successfully', {
                requestId: reqId,
                model: response.model,
                provider: response.provider,
                duration,
                tokenUsage: response.usage,
            });
            return response;
        }
        catch (error) {
            const duration = (0, utils_1.calculateDuration)(startTime);
            // Record failed metrics
            this.recordMetrics({
                requestId: reqId,
                provider: (request.model ? adapters_1.adapterManager.getModelConfig(request.model)?.provider : undefined) || 'openai',
                model: request.model || 'unknown',
                startTime: startTime,
                endTime: (0, utils_1.getPerformanceTimestamp)(),
                duration,
                success: false,
                error: error.message,
            });
            // Log error
            (0, logger_1.logError)(error, reqId, undefined, request.model);
            (0, logger_1.logResponse)(reqId, error.statusCode || 500, duration);
            throw error;
        }
    }
    /**
     * Process streaming chat completion request
     */
    async *chatCompletionStream(request, requestId) {
        const reqId = requestId || (0, utils_1.generateRequestId)();
        const startTime = (0, utils_1.getPerformanceTimestamp)();
        let chunkCount = 0;
        let totalTokens = 0;
        try {
            // Validate request
            adapters_1.adapterManager.validateRequest(request);
            // Log request
            (0, logger_1.logRequest)(reqId, 'POST', '/v1/chat/completions');
            // Get model configuration
            const modelConfig = adapters_1.adapterManager.getModelConfig(request.model);
            if (!modelConfig) {
                throw new types_1.ValidationError(`Model '${request.model}' not found`);
            }
            logger_1.logger.info('Processing streaming chat completion request', {
                requestId: reqId,
                model: request.model,
                provider: modelConfig.provider,
                messageCount: request.messages.length,
                stream: true,
            });
            // Process request through adapter manager
            const stream = adapters_1.adapterManager.chatCompletionStream(request);
            for await (const chunk of stream) {
                chunkCount++;
                // Track token usage if available (usually in the last chunk)
                if (chunk.choices[0]?.finishReason) {
                    // This is typically the last chunk, estimate tokens
                    totalTokens = this.estimateTokens(request.messages, chunk);
                }
                yield chunk;
            }
            const duration = (0, utils_1.calculateDuration)(startTime);
            // Record metrics
            this.recordMetrics({
                requestId: reqId,
                provider: modelConfig.provider,
                model: request.model,
                startTime: startTime,
                endTime: (0, utils_1.getPerformanceTimestamp)(),
                duration,
                tokenUsage: totalTokens > 0 ? {
                    prompt: Math.floor(totalTokens * 0.7), // Estimate
                    completion: Math.floor(totalTokens * 0.3), // Estimate
                    total: totalTokens,
                } : undefined,
                success: true,
            });
            // Log response
            (0, logger_1.logResponse)(reqId, 200, duration, modelConfig.provider, request.model);
            logger_1.logger.info('Streaming chat completion request completed successfully', {
                requestId: reqId,
                model: request.model,
                provider: modelConfig.provider,
                duration,
                chunkCount,
                estimatedTokens: totalTokens,
            });
        }
        catch (error) {
            const duration = (0, utils_1.calculateDuration)(startTime);
            // Record failed metrics
            this.recordMetrics({
                requestId: reqId,
                provider: (request.model ? adapters_1.adapterManager.getModelConfig(request.model)?.provider : undefined) || 'openai',
                model: request.model || 'unknown',
                startTime: startTime,
                endTime: (0, utils_1.getPerformanceTimestamp)(),
                duration,
                success: false,
                error: error.message,
            });
            // Log error
            (0, logger_1.logError)(error, reqId, undefined, request.model);
            (0, logger_1.logResponse)(reqId, error.statusCode || 500, duration);
            throw error;
        }
    }
    /**
     * Get available models
     */
    getAvailableModels() {
        return adapters_1.adapterManager.getAllModelsInfo();
    }
    /**
     * Get model information
     */
    getModelInfo(modelId) {
        return adapters_1.adapterManager.getModelInfo(modelId);
    }
    /**
     * Get service health status
     */
    getHealthStatus() {
        return adapters_1.adapterManager.getHealthStatus();
    }
    /**
     * Refresh provider health status
     */
    async refreshHealth() {
        await adapters_1.adapterManager.refreshHealth();
    }
    /**
     * Get request metrics
     */
    getMetrics() {
        const metrics = Array.from(this.metrics.values());
        const summary = {
            totalRequests: metrics.length,
            successfulRequests: metrics.filter(m => m.success).length,
            failedRequests: metrics.filter(m => !m.success).length,
            averageDuration: metrics.length > 0
                ? metrics.reduce((sum, m) => sum + (m.duration || 0), 0) / metrics.length
                : 0,
            totalTokens: metrics.reduce((sum, m) => sum + (m.tokenUsage?.total || 0), 0),
            byProvider: this.getMetricsByProvider(metrics),
            byModel: this.getMetricsByModel(metrics),
            recentErrors: metrics
                .filter(m => !m.success && m.error)
                .slice(-10)
                .map(m => ({
                requestId: m.requestId,
                model: m.model,
                provider: m.provider,
                error: m.error,
                timestamp: m.startTime,
            })),
        };
        return summary;
    }
    /**
     * Clear metrics history
     */
    clearMetrics() {
        this.metrics.clear();
        logger_1.logger.info('Metrics history cleared');
    }
    recordMetrics(metrics) {
        this.metrics.set(metrics.requestId, metrics);
        // Cleanup old metrics if we exceed the limit
        if (this.metrics.size > this.maxMetricsHistory) {
            const oldestKey = this.metrics.keys().next().value;
            if (oldestKey) {
                this.metrics.delete(oldestKey);
            }
        }
    }
    estimateTokens(messages, _chunk) {
        // Simple token estimation based on character count
        // This is a rough estimate, real token counting would require the actual tokenizer
        const totalChars = messages.reduce((sum, msg) => sum + msg.content.length, 0);
        return Math.ceil(totalChars / 4); // Rough estimate: 1 token ≈ 4 characters
    }
    getMetricsByProvider(metrics) {
        const byProvider = {};
        for (const metric of metrics) {
            if (!byProvider[metric.provider]) {
                byProvider[metric.provider] = {
                    requests: 0,
                    successful: 0,
                    failed: 0,
                    totalDuration: 0,
                    totalTokens: 0,
                };
            }
            const providerMetrics = byProvider[metric.provider];
            providerMetrics.requests++;
            if (metric.success) {
                providerMetrics.successful++;
            }
            else {
                providerMetrics.failed++;
            }
            providerMetrics.totalDuration += metric.duration || 0;
            providerMetrics.totalTokens += metric.tokenUsage?.total || 0;
        }
        // Calculate averages
        for (const provider in byProvider) {
            const metrics = byProvider[provider];
            metrics.averageDuration = metrics.requests > 0 ? metrics.totalDuration / metrics.requests : 0;
            metrics.successRate = metrics.requests > 0 ? metrics.successful / metrics.requests : 0;
        }
        return byProvider;
    }
    getMetricsByModel(metrics) {
        const byModel = {};
        for (const metric of metrics) {
            if (!byModel[metric.model]) {
                byModel[metric.model] = {
                    requests: 0,
                    successful: 0,
                    failed: 0,
                    totalDuration: 0,
                    totalTokens: 0,
                };
            }
            const modelMetrics = byModel[metric.model];
            modelMetrics.requests++;
            if (metric.success) {
                modelMetrics.successful++;
            }
            else {
                modelMetrics.failed++;
            }
            modelMetrics.totalDuration += metric.duration || 0;
            modelMetrics.totalTokens += metric.tokenUsage?.total || 0;
        }
        // Calculate averages
        for (const model in byModel) {
            const metrics = byModel[model];
            metrics.averageDuration = metrics.requests > 0 ? metrics.totalDuration / metrics.requests : 0;
            metrics.successRate = metrics.requests > 0 ? metrics.successful / metrics.requests : 0;
        }
        return byModel;
    }
}
exports.AIService = AIService;
// Export singleton instance
exports.aiService = new AIService();
//# sourceMappingURL=ai.js.map