import { ChatCompletionRequest, ChatCompletionResponse, StreamChunk } from '@/types';
export declare class AIService {
    private metrics;
    private readonly maxMetricsHistory;
    /**
     * Process chat completion request
     */
    chatCompletion(request: ChatCompletionRequest, requestId?: string): Promise<ChatCompletionResponse>;
    /**
     * Process streaming chat completion request
     */
    chatCompletionStream(request: ChatCompletionRequest, requestId?: string): AsyncGenerator<StreamChunk>;
    /**
     * Get available models
     */
    getAvailableModels(): {
        providerHealthy: boolean;
        lastHealthCheck: number | undefined;
        id: string;
        name: string;
        provider: import("@/types").AIProvider;
        endpoint?: string;
        authMethod: import("@/types").AuthMethod;
        maxTokens?: number;
        temperature?: number;
        topP?: number;
        topK?: number;
        enabled: boolean;
        fallbackModels?: string[];
        rateLimits?: {
            requestsPerMinute: number;
            tokensPerMinute: number;
        };
    }[];
    /**
     * Get model information
     */
    getModelInfo(modelId: string): {
        providerHealthy: boolean;
        lastHealthCheck: number | undefined;
        id: string;
        name: string;
        provider: import("@/types").AIProvider;
        endpoint?: string;
        authMethod: import("@/types").AuthMethod;
        maxTokens?: number;
        temperature?: number;
        topP?: number;
        topK?: number;
        enabled: boolean;
        fallbackModels?: string[];
        rateLimits?: {
            requestsPerMinute: number;
            tokensPerMinute: number;
        };
    } | null;
    /**
     * Get service health status
     */
    getHealthStatus(): {
        adapters: import("@/types").AIProvider[];
        health: Record<import("@/types").AIProvider, {
            healthy: boolean;
            lastCheck: number;
        }>;
        models: {
            total: number;
            enabled: number;
            byProvider: {
                [k: string]: number;
            };
        };
    };
    /**
     * Refresh provider health status
     */
    refreshHealth(): Promise<void>;
    /**
     * Get request metrics
     */
    getMetrics(): {
        totalRequests: number;
        successfulRequests: number;
        failedRequests: number;
        averageDuration: number;
        totalTokens: number;
        byProvider: Record<string, any>;
        byModel: Record<string, any>;
        recentErrors: {
            requestId: string;
            model: string;
            provider: import("@/types").AIProvider;
            error: string | undefined;
            timestamp: number;
        }[];
    };
    /**
     * Clear metrics history
     */
    clearMetrics(): void;
    private recordMetrics;
    private estimateTokens;
    private getMetricsByProvider;
    private getMetricsByModel;
}
export declare const aiService: AIService;
//# sourceMappingURL=ai.d.ts.map