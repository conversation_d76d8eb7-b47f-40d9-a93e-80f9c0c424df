"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startServer = exports.buildApp = void 0;
const fastify_1 = __importDefault(require("fastify"));
const fastify_sse_v2_1 = require("fastify-sse-v2");
const cors_1 = __importDefault(require("@fastify/cors"));
const helmet_1 = __importDefault(require("@fastify/helmet"));
const rate_limit_1 = __importDefault(require("@fastify/rate-limit"));
const swagger_1 = __importDefault(require("@fastify/swagger"));
const swagger_ui_1 = __importDefault(require("@fastify/swagger-ui"));
const under_pressure_1 = __importDefault(require("@fastify/under-pressure"));
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
const middleware_1 = require("./middleware");
const routes_1 = require("./controllers/routes");
/**
 * Build Fastify application with all plugins and routes
 */
const buildApp = async () => {
    // Create Fastify instance
    const app = (0, fastify_1.default)({
        logger: false, // We use our custom logger
        trustProxy: true,
        bodyLimit: 10 * 1024 * 1024, // 10MB
        keepAliveTimeout: 30000,
        connectionTimeout: 30000,
    });
    try {
        // Register service configuration
        app.decorate('config', config_1.serviceConfig);
        // Register SSE plugin first
        await app.register(fastify_sse_v2_1.FastifySSEPlugin, {
            retryDelay: 3000,
        });
        // Register CORS
        await app.register(cors_1.default, {
            origin: true,
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: [
                'Content-Type',
                'Authorization',
                'X-Requested-With',
                'X-Request-ID',
                'Accept',
                'Origin',
            ],
            exposedHeaders: ['X-Request-ID'],
        });
        // Register security middleware (Helmet)
        if (config_1.serviceConfig.security.helmetEnabled) {
            await app.register(helmet_1.default, {
                contentSecurityPolicy: {
                    directives: {
                        defaultSrc: ["'self'"],
                        styleSrc: ["'self'", "'unsafe-inline'"],
                        scriptSrc: ["'self'"],
                        imgSrc: ["'self'", "data:", "https:"],
                        connectSrc: ["'self'"],
                        fontSrc: ["'self'"],
                        objectSrc: ["'none'"],
                        mediaSrc: ["'self'"],
                        frameSrc: ["'none'"],
                    },
                },
                crossOriginEmbedderPolicy: false,
                crossOriginOpenerPolicy: false,
                crossOriginResourcePolicy: false,
                frameguard: { action: 'deny' },
                hidePoweredBy: true,
                hsts: {
                    maxAge: 31536000,
                    includeSubDomains: true,
                    preload: true,
                },
                ieNoOpen: true,
                noSniff: true,
                originAgentCluster: true,
                permittedCrossDomainPolicies: false,
                referrerPolicy: { policy: 'no-referrer' },
                xssFilter: true,
            });
        }
        // Register rate limiting
        await app.register(rate_limit_1.default, {
            ...middleware_1.rateLimitMiddleware,
            max: config_1.serviceConfig.rateLimit.max,
            timeWindow: config_1.serviceConfig.rateLimit.timeWindow,
        });
        // Register under pressure (load monitoring)
        await app.register(under_pressure_1.default, {
            maxEventLoopDelay: 1000,
            maxHeapUsedBytes: **********, // 1GB
            maxRssBytes: **********, // 1GB
            maxEventLoopUtilization: 0.98,
            message: 'Service under pressure',
            retryAfter: 50,
            healthCheck: async () => {
                // Custom health check logic
                return true;
            },
            healthCheckInterval: 5000,
        });
        // Register Swagger documentation
        await app.register(swagger_1.default, {
            openapi: {
                openapi: '3.0.0',
                info: {
                    title: 'AI Proxy Service API',
                    description: 'High-performance AI proxy service with dual-mode responses and multi-provider support',
                    version: process.env['npm_package_version'] || '1.0.0',
                    contact: {
                        name: 'AI Proxy Service Team',
                        email: '<EMAIL>',
                    },
                    license: {
                        name: 'MIT',
                        url: 'https://opensource.org/licenses/MIT',
                    },
                },
                servers: [
                    {
                        url: `http://localhost:${config_1.serviceConfig.server.port}`,
                        description: 'Development server',
                    },
                ],
                tags: [
                    {
                        name: 'Chat',
                        description: 'Chat completion endpoints',
                    },
                    {
                        name: 'Models',
                        description: 'Model management endpoints',
                    },
                    {
                        name: 'Health',
                        description: 'Health check endpoints',
                    },
                    {
                        name: 'Monitoring',
                        description: 'Monitoring and metrics endpoints',
                    },
                    {
                        name: 'General',
                        description: 'General API endpoints',
                    },
                ],
                components: {
                    securitySchemes: {
                        bearerAuth: {
                            type: 'http',
                            scheme: 'bearer',
                            bearerFormat: 'JWT',
                        },
                        apiKey: {
                            type: 'apiKey',
                            in: 'header',
                            name: 'X-API-Key',
                        },
                    },
                },
                security: [
                    {
                        bearerAuth: [],
                    },
                    {
                        apiKey: [],
                    },
                ],
            },
        });
        // Register Swagger UI
        await app.register(swagger_ui_1.default, {
            routePrefix: '/docs',
            uiConfig: {
                docExpansion: 'list',
                deepLinking: false,
            },
            staticCSP: true,
            transformStaticCSP: (header) => header,
            transformSpecification: (swaggerObject) => {
                return swaggerObject;
            },
            transformSpecificationClone: true,
        });
        // Register custom middleware
        await (0, middleware_1.registerMiddleware)(app);
        // Register all routes
        await (0, routes_1.registerRoutes)(app);
        // Global error handler for uncaught errors
        process.on('uncaughtException', (error) => {
            logger_1.logger.error('Uncaught exception', { error });
            process.exit(1);
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger_1.logger.error('Unhandled rejection', { reason, promise });
            process.exit(1);
        });
        // Graceful shutdown handling
        const gracefulShutdown = async (signal) => {
            logger_1.logger.info(`Received ${signal}, starting graceful shutdown`);
            try {
                await app.close();
                logger_1.logger.info('Fastify server closed successfully');
                process.exit(0);
            }
            catch (error) {
                logger_1.logger.error('Error during graceful shutdown', { error });
                process.exit(1);
            }
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
        logger_1.logger.info('Fastify application built successfully', {
            environment: config_1.ENV.NODE_ENV,
            port: config_1.serviceConfig.server.port,
            host: config_1.serviceConfig.server.host,
            logLevel: config_1.serviceConfig.server.logLevel,
        });
        return app;
    }
    catch (error) {
        logger_1.logger.error('Failed to build Fastify application', { error });
        throw error;
    }
};
exports.buildApp = buildApp;
/**
 * Start the server
 */
const startServer = async () => {
    try {
        const app = await (0, exports.buildApp)();
        const address = await app.listen({
            port: config_1.serviceConfig.server.port,
            host: config_1.serviceConfig.server.host,
        });
        logger_1.logger.info('Server started successfully', {
            address,
            environment: config_1.ENV.NODE_ENV,
            pid: process.pid,
            nodeVersion: process.version,
            platform: process.platform,
            arch: process.arch,
        });
        // Log available routes
        logger_1.logger.info('Available routes:', {
            routes: app.printRoutes({ commonPrefix: false }),
        });
        // Log service configuration summary
        logger_1.logger.info('Service configuration summary', {
            providers: Object.keys(config_1.serviceConfig.providers),
            modelsCount: config_1.serviceConfig.models.length,
            enabledModels: config_1.serviceConfig.models.filter(m => m.enabled).length,
            rateLimit: config_1.serviceConfig.rateLimit,
            security: config_1.serviceConfig.security,
            monitoring: config_1.serviceConfig.monitoring,
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to start server', { error });
        process.exit(1);
    }
};
exports.startServer = startServer;
// Start server if this file is run directly
if (require.main === module) {
    (0, exports.startServer)();
}
exports.default = { buildApp: exports.buildApp, startServer: exports.startServer };
//# sourceMappingURL=app.js.map