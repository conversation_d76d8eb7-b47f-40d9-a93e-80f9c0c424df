"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeminiAdapter = void 0;
const genai_1 = require("@google/genai");
const types_1 = require("@/types");
const config_1 = require("@/config");
const logger_1 = require("@/utils/logger");
const utils_1 = require("@/utils");
class GeminiAdapter {
    provider = 'gemini';
    client;
    isInitialized = false;
    constructor() {
        this.initializeClient();
    }
    initializeClient() {
        try {
            const config = config_1.serviceConfig.providers.gemini;
            // Initialize based on auth method
            if (config.authMethod === 'api-key') {
                this.client = new genai_1.GoogleGenAI({
                    apiKey: config.apiKey,
                });
            }
            else if (config.authMethod === 'google-auth' && config.project) {
                this.client = new genai_1.GoogleGenAI({
                    vertexai: true,
                    project: config.project,
                    location: config.location || 'us-central1',
                });
            }
            else {
                throw new Error('Invalid Gemini configuration');
            }
            this.isInitialized = true;
            logger_1.logger.info('Gemini adapter initialized successfully', {
                authMethod: config.authMethod,
                project: config.project,
                location: config.location,
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize Gemini adapter', { error });
            throw new types_1.ProviderError('Failed to initialize Gemini adapter', 'gemini', error);
        }
    }
    async isHealthy() {
        if (!this.isInitialized) {
            return false;
        }
        try {
            const startTime = (0, utils_1.getPerformanceTimestamp)();
            // Simple health check using a basic generation request
            const response = await (0, utils_1.withTimeout)(this.client.models.generateContent({
                model: 'gemini-1.5-flash',
                contents: 'Hello',
                config: {
                    maxOutputTokens: 1,
                },
            }), 5000, 'Gemini health check timeout');
            const duration = (0, utils_1.calculateDuration)(startTime);
            (0, logger_1.logProviderCall)(this.provider, 'health-check', (0, utils_1.generateRequestId)(), 'health-check', duration, true);
            return !!response;
        }
        catch (error) {
            logger_1.logger.warn('Gemini health check failed', { error });
            return false;
        }
    }
    validateRequest(request) {
        (0, utils_1.validateChatCompletionRequest)(request);
        // Gemini-specific validations
        if (request.presencePenalty !== undefined) {
            throw new types_1.ValidationError('Gemini does not support presencePenalty parameter');
        }
        if (request.frequencyPenalty !== undefined) {
            throw new types_1.ValidationError('Gemini does not support frequencyPenalty parameter');
        }
        if (request.tools !== undefined) {
            logger_1.logger.warn('Gemini tools support is experimental', { model: request.model });
        }
    }
    async chatCompletion(request) {
        const requestId = (0, utils_1.generateRequestId)();
        const startTime = (0, utils_1.getPerformanceTimestamp)();
        try {
            this.validateRequest(request);
            const geminiRequest = this.transformRequest(request);
            const response = await (0, utils_1.retryWithBackoff)(() => (0, utils_1.withTimeout)(this.client.models.generateContent(geminiRequest), config_1.serviceConfig.providers.gemini.timeout, 'Gemini request timeout'), {
                retries: config_1.serviceConfig.providers.gemini.maxRetries,
                onFailedAttempt: (error, attemptNumber) => {
                    logger_1.logger.warn('Gemini request attempt failed', {
                        requestId,
                        attemptNumber,
                        error: error.message,
                        model: request.model,
                    });
                },
            });
            const duration = (0, utils_1.calculateDuration)(startTime);
            const transformedResponse = this.transformResponse(response, request.model, duration);
            (0, logger_1.logProviderCall)(this.provider, request.model, requestId, 'chat-completion', duration, true);
            return transformedResponse;
        }
        catch (error) {
            const duration = (0, utils_1.calculateDuration)(startTime);
            (0, logger_1.logProviderCall)(this.provider, request.model, requestId, 'chat-completion', duration, false, error.message);
            throw this.handleError(error, requestId);
        }
    }
    async *chatCompletionStream(request) {
        const requestId = (0, utils_1.generateRequestId)();
        const startTime = (0, utils_1.getPerformanceTimestamp)();
        try {
            this.validateRequest(request);
            const geminiRequest = this.transformRequest(request);
            const stream = await (0, utils_1.retryWithBackoff)(() => (0, utils_1.withTimeout)(this.client.models.generateContentStream(geminiRequest), config_1.serviceConfig.providers.gemini.timeout, 'Gemini stream request timeout'), {
                retries: config_1.serviceConfig.providers.gemini.maxRetries,
                onFailedAttempt: (error, attemptNumber) => {
                    logger_1.logger.warn('Gemini stream request attempt failed', {
                        requestId,
                        attemptNumber,
                        error: error.message,
                        model: request.model,
                    });
                },
            });
            let chunkCount = 0;
            for await (const chunk of stream) {
                chunkCount++;
                yield this.transformStreamChunk(chunk, request.model, chunkCount);
            }
            const duration = (0, utils_1.calculateDuration)(startTime);
            (0, logger_1.logProviderCall)(this.provider, request.model, requestId, 'chat-completion-stream', duration, true);
        }
        catch (error) {
            const duration = (0, utils_1.calculateDuration)(startTime);
            (0, logger_1.logProviderCall)(this.provider, request.model, requestId, 'chat-completion-stream', duration, false, error.message);
            throw this.handleError(error, requestId);
        }
    }
    transformRequest(request) {
        // Convert messages to Gemini format
        const contents = this.convertMessagesToContents(request.messages);
        const geminiRequest = {
            model: request.model,
            contents,
        };
        // Build generation config
        const config = {};
        if (request.temperature !== undefined) {
            config.temperature = request.temperature;
        }
        if (request.maxTokens !== undefined) {
            config.maxOutputTokens = request.maxTokens;
        }
        if (request.topP !== undefined) {
            config.topP = request.topP;
        }
        if (request.topK !== undefined) {
            config.topK = request.topK;
        }
        if (request.stop !== undefined) {
            config.stopSequences = Array.isArray(request.stop) ? request.stop : [request.stop];
        }
        if (Object.keys(config).length > 0) {
            geminiRequest.config = config;
        }
        return geminiRequest;
    }
    convertMessagesToContents(messages) {
        const contents = [];
        let systemInstruction = '';
        for (const message of messages) {
            if (message.role === 'system') {
                systemInstruction += message.content + '\n';
                continue;
            }
            const role = message.role === 'assistant' ? 'model' : 'user';
            contents.push({
                role,
                parts: [{ text: message.content }],
            });
        }
        // Add system instruction as the first user message if present
        if (systemInstruction.trim()) {
            contents.unshift({
                role: 'user',
                parts: [{ text: `System: ${systemInstruction.trim()}` }],
            });
        }
        return contents;
    }
    transformResponse(response, model, processingTime) {
        const candidate = response.candidates?.[0];
        const content = candidate?.content?.parts?.[0]?.text || '';
        return {
            id: (0, utils_1.generateRequestId)(),
            object: 'chat.completion',
            created: (0, utils_1.getCurrentTimestamp)(),
            model,
            choices: [{
                    index: 0,
                    message: {
                        role: 'assistant',
                        content,
                    },
                    finishReason: this.mapFinishReason(candidate?.finishReason),
                }],
            usage: {
                promptTokens: response.usageMetadata?.promptTokenCount || 0,
                completionTokens: response.usageMetadata?.candidatesTokenCount || 0,
                totalTokens: response.usageMetadata?.totalTokenCount || 0,
            },
            provider: this.provider,
            processingTime,
        };
    }
    transformStreamChunk(chunk, model, index) {
        const candidate = chunk.candidates?.[0];
        const content = candidate?.content?.parts?.[0]?.text || '';
        return {
            id: (0, utils_1.generateRequestId)(),
            object: 'chat.completion.chunk',
            created: (0, utils_1.getCurrentTimestamp)(),
            model,
            choices: [{
                    index: 0,
                    delta: {
                        role: index === 1 ? 'assistant' : undefined,
                        content,
                    },
                    finishReason: this.mapFinishReason(candidate?.finishReason),
                }],
            provider: this.provider,
        };
    }
    mapFinishReason(geminiReason) {
        if (!geminiReason)
            return null;
        const reasonMap = {
            'STOP': 'stop',
            'MAX_TOKENS': 'length',
            'SAFETY': 'content_filter',
            'RECITATION': 'content_filter',
            'OTHER': 'stop',
        };
        return reasonMap[geminiReason] || 'stop';
    }
    handleError(error, requestId) {
        logger_1.logger.error('Gemini adapter error', { error, requestId });
        // Handle specific Gemini errors
        if (error.message.includes('timeout')) {
            return new types_1.ProviderError('Gemini request timeout', this.provider, error);
        }
        if (error.message.includes('quota')) {
            return new types_1.ProviderError('Gemini quota exceeded', this.provider, error);
        }
        if (error.message.includes('API key')) {
            return new types_1.ProviderError('Invalid Gemini API key', this.provider, error);
        }
        if (error.message.includes('model')) {
            return new types_1.ProviderError('Gemini model not found or not supported', this.provider, error);
        }
        return new types_1.ProviderError(`Gemini error: ${error.message}`, this.provider, error);
    }
}
exports.GeminiAdapter = GeminiAdapter;
//# sourceMappingURL=gemini.js.map