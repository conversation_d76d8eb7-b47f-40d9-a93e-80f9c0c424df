{"version": 3, "file": "gemini.js", "sourceRoot": "", "sources": ["../../src/adapters/gemini.ts"], "names": [], "mappings": ";;;AAAA,yCAA4C;AAC5C,mCAOiB;AACjB,qCAAyC;AACzC,2CAAyD;AACzD,mCAQiB;AAEjB,MAAa,aAAa;IACR,QAAQ,GAAG,QAAiB,CAAC;IACrC,MAAM,CAAe;IACrB,aAAa,GAAG,KAAK,CAAC;IAE9B;QACE,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,sBAAa,CAAC,SAAS,CAAC,MAAM,CAAC;YAE9C,kCAAkC;YAClC,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBACpC,IAAI,CAAC,MAAM,GAAG,IAAI,mBAAW,CAAC;oBAC5B,MAAM,EAAE,MAAM,CAAC,MAAM;iBACtB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,MAAM,CAAC,UAAU,KAAK,aAAa,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjE,IAAI,CAAC,MAAM,GAAG,IAAI,mBAAW,CAAC;oBAC5B,QAAQ,EAAE,IAAI;oBACd,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,aAAa;iBAC3C,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAClD,CAAC;YAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,eAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE;gBACrD,UAAU,EAAE,MAAM,CAAC,UAAU;gBAC7B,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC/D,MAAM,IAAI,qBAAa,CAAC,qCAAqC,EAAE,QAAQ,EAAE,KAAc,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS;QACb,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAA,+BAAuB,GAAE,CAAC;YAE5C,uDAAuD;YACvD,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAW,EAChC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC;gBACjC,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,OAAO;gBACjB,MAAM,EAAE;oBACN,eAAe,EAAE,CAAC;iBACnB;aACF,CAAC,EACF,IAAI,EACJ,6BAA6B,CAC9B,CAAC;YAEF,MAAM,QAAQ,GAAG,IAAA,yBAAiB,EAAC,SAAS,CAAC,CAAC;YAE9C,IAAA,wBAAe,EACb,IAAI,CAAC,QAAQ,EACb,cAAc,EACd,IAAA,yBAAiB,GAAE,EACnB,cAAc,EACd,QAAQ,EACR,IAAI,CACL,CAAC;YAEF,OAAO,CAAC,CAAC,QAAQ,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,eAAe,CAAC,OAA8B;QAC5C,IAAA,qCAA6B,EAAC,OAAO,CAAC,CAAC;QAEvC,8BAA8B;QAC9B,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YAC1C,MAAM,IAAI,uBAAe,CAAC,mDAAmD,CAAC,CAAC;QACjF,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC3C,MAAM,IAAI,uBAAe,CAAC,oDAAoD,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAChC,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAA8B;QACjD,MAAM,SAAS,GAAG,IAAA,yBAAiB,GAAE,CAAC;QACtC,MAAM,SAAS,GAAG,IAAA,+BAAuB,GAAE,CAAC;QAE5C,IAAI,CAAC;YACH,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAE9B,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAErD,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAgB,EACrC,GAAG,EAAE,CAAC,IAAA,mBAAW,EACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,EACjD,sBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EACtC,wBAAwB,CACzB,EACD;gBACE,OAAO,EAAE,sBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU;gBAClD,eAAe,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE;oBACxC,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;wBAC3C,SAAS;wBACT,aAAa;wBACb,KAAK,EAAE,KAAK,CAAC,OAAO;wBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;qBACrB,CAAC,CAAC;gBACL,CAAC;aACF,CACF,CAAC;YAEF,MAAM,QAAQ,GAAG,IAAA,yBAAiB,EAAC,SAAS,CAAC,CAAC;YAC9C,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAEtF,IAAA,wBAAe,EACb,IAAI,CAAC,QAAQ,EACb,OAAO,CAAC,KAAK,EACb,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACR,IAAI,CACL,CAAC;YAEF,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAA,yBAAiB,EAAC,SAAS,CAAC,CAAC;YAE9C,IAAA,wBAAe,EACb,IAAI,CAAC,QAAQ,EACb,OAAO,CAAC,KAAK,EACb,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACR,KAAK,EACJ,KAAe,CAAC,OAAO,CACzB,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,KAAc,EAAE,SAAS,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,KAAK,CAAA,CAAE,oBAAoB,CAAC,OAA8B;QACxD,MAAM,SAAS,GAAG,IAAA,yBAAiB,GAAE,CAAC;QACtC,MAAM,SAAS,GAAG,IAAA,+BAAuB,GAAE,CAAC;QAE5C,IAAI,CAAC;YACH,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAE9B,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAErD,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAgB,EACnC,GAAG,EAAE,CAAC,IAAA,mBAAW,EACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,aAAa,CAAC,EACvD,sBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EACtC,+BAA+B,CAChC,EACD;gBACE,OAAO,EAAE,sBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU;gBAClD,eAAe,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE;oBACxC,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE;wBAClD,SAAS;wBACT,aAAa;wBACb,KAAK,EAAE,KAAK,CAAC,OAAO;wBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;qBACrB,CAAC,CAAC;gBACL,CAAC;aACF,CACF,CAAC;YAEF,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,UAAU,EAAE,CAAC;gBACb,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YACpE,CAAC;YAED,MAAM,QAAQ,GAAG,IAAA,yBAAiB,EAAC,SAAS,CAAC,CAAC;YAE9C,IAAA,wBAAe,EACb,IAAI,CAAC,QAAQ,EACb,OAAO,CAAC,KAAK,EACb,SAAS,EACT,wBAAwB,EACxB,QAAQ,EACR,IAAI,CACL,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAA,yBAAiB,EAAC,SAAS,CAAC,CAAC;YAE9C,IAAA,wBAAe,EACb,IAAI,CAAC,QAAQ,EACb,OAAO,CAAC,KAAK,EACb,SAAS,EACT,wBAAwB,EACxB,QAAQ,EACR,KAAK,EACJ,KAAe,CAAC,OAAO,CACzB,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,KAAc,EAAE,SAAS,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,OAA8B;QACrD,oCAAoC;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAElE,MAAM,aAAa,GAAQ;YACzB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,QAAQ;SACT,CAAC;QAEF,0BAA0B;QAC1B,MAAM,MAAM,GAAQ,EAAE,CAAC;QAEvB,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACtC,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAC3C,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACpC,MAAM,CAAC,eAAe,GAAG,OAAO,CAAC,SAAS,CAAC;QAC7C,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC/B,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC;QAChC,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,yBAAyB,CAAC,QAAe;QAC/C,MAAM,QAAQ,GAAU,EAAE,CAAC;QAC3B,IAAI,iBAAiB,GAAG,EAAE,CAAC;QAE3B,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9B,iBAAiB,IAAI,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;gBAC5C,SAAS;YACX,CAAC;YAED,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;YAE7D,QAAQ,CAAC,IAAI,CAAC;gBACZ,IAAI;gBACJ,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC;aACnC,CAAC,CAAC;QACL,CAAC;QAED,8DAA8D;QAC9D,IAAI,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC;YAC7B,QAAQ,CAAC,OAAO,CAAC;gBACf,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,iBAAiB,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC;aACzD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,iBAAiB,CAAC,QAAa,EAAE,KAAa,EAAE,cAAsB;QAC5E,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;QAE3D,OAAO;YACL,EAAE,EAAE,IAAA,yBAAiB,GAAE;YACvB,MAAM,EAAE,iBAAiB;YACzB,OAAO,EAAE,IAAA,2BAAmB,GAAE;YAC9B,KAAK;YACL,OAAO,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;oBACR,OAAO,EAAE;wBACP,IAAI,EAAE,WAAW;wBACjB,OAAO;qBACR;oBACD,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,YAAY,CAAC;iBAC5D,CAAC;YACF,KAAK,EAAE;gBACL,YAAY,EAAE,QAAQ,CAAC,aAAa,EAAE,gBAAgB,IAAI,CAAC;gBAC3D,gBAAgB,EAAE,QAAQ,CAAC,aAAa,EAAE,oBAAoB,IAAI,CAAC;gBACnE,WAAW,EAAE,QAAQ,CAAC,aAAa,EAAE,eAAe,IAAI,CAAC;aAC1D;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc;SACf,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,KAAU,EAAE,KAAa,EAAE,KAAa;QACnE,MAAM,SAAS,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;QAE3D,OAAO;YACL,EAAE,EAAE,IAAA,yBAAiB,GAAE;YACvB,MAAM,EAAE,uBAAuB;YAC/B,OAAO,EAAE,IAAA,2BAAmB,GAAE;YAC9B,KAAK;YACL,OAAO,EAAE,CAAC;oBACR,KAAK,EAAE,CAAC;oBACR,KAAK,EAAE;wBACL,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,WAAoB,EAAE,CAAC;wBAClD,OAAO;qBACR;oBACD,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,YAAY,CAAC;iBAC5D,CAAC;YACF,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;IACJ,CAAC;IAEO,eAAe,CAAC,YAAgC;QACtD,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAE/B,MAAM,SAAS,GAA2B;YACxC,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,QAAQ;YACtB,QAAQ,EAAE,gBAAgB;YAC1B,YAAY,EAAE,gBAAgB;YAC9B,OAAO,EAAE,MAAM;SAChB,CAAC;QAEF,OAAO,SAAS,CAAC,YAAY,CAAC,IAAI,MAAM,CAAC;IAC3C,CAAC;IAEO,WAAW,CAAC,KAAY,EAAE,SAAiB;QACjD,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;QAE3D,gCAAgC;QAChC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,OAAO,IAAI,qBAAa,CAAC,wBAAwB,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,qBAAa,CAAC,uBAAuB,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,OAAO,IAAI,qBAAa,CAAC,wBAAwB,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,qBAAa,CAAC,yCAAyC,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC5F,CAAC;QAED,OAAO,IAAI,qBAAa,CAAC,iBAAiB,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACnF,CAAC;CACF;AAjXD,sCAiXC"}