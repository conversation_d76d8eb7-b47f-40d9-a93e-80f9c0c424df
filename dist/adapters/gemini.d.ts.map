{"version": 3, "file": "gemini.d.ts", "sourceRoot": "", "sources": ["../../src/adapters/gemini.ts"], "names": [], "mappings": "AACA,OAAO,EACL,iBAAiB,EACjB,qBAAqB,EACrB,sBAAsB,EACtB,WAAW,EAGZ,MAAM,SAAS,CAAC;AAajB,qBAAa,aAAc,YAAW,iBAAiB;IACrD,SAAgB,QAAQ,EAAG,QAAQ,CAAU;IAC7C,OAAO,CAAC,MAAM,CAAe;IAC7B,OAAO,CAAC,aAAa,CAAS;;IAM9B,OAAO,CAAC,gBAAgB;IA+BlB,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC;IAuCnC,eAAe,CAAC,OAAO,EAAE,qBAAqB,GAAG,IAAI;IAiB/C,cAAc,CAAC,OAAO,EAAE,qBAAqB,GAAG,OAAO,CAAC,sBAAsB,CAAC;IA0D9E,oBAAoB,CAAC,OAAO,EAAE,qBAAqB,GAAG,cAAc,CAAC,WAAW,CAAC;IA+DxF,OAAO,CAAC,gBAAgB;IAuCxB,OAAO,CAAC,yBAAyB;IA6BjC,OAAO,CAAC,iBAAiB;IA2BzB,OAAO,CAAC,oBAAoB;IAqB5B,OAAO,CAAC,eAAe;IAcvB,OAAO,CAAC,WAAW;CAsBpB"}