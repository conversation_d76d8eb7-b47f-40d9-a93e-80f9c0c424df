"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeminiAdapter = exports.OpenAIAdapter = exports.adapterManager = exports.AdapterManager = void 0;
const openai_1 = require("./openai");
Object.defineProperty(exports, "OpenAIAdapter", { enumerable: true, get: function () { return openai_1.OpenAIAdapter; } });
const gemini_1 = require("./gemini");
Object.defineProperty(exports, "GeminiAdapter", { enumerable: true, get: function () { return gemini_1.GeminiAdapter; } });
const types_1 = require("../types");
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
class AdapterManager {
    adapters = new Map();
    modelConfigs = new Map();
    healthStatus = new Map();
    lastHealthCheck = new Map();
    healthCheckInterval = 60000; // 1 minute
    constructor() {
        this.initializeAdapters();
        this.initializeModelConfigs();
        this.startHealthChecks();
    }
    initializeAdapters() {
        try {
            // Initialize OpenAI adapter
            const openaiAdapter = new openai_1.OpenAIAdapter();
            this.adapters.set('openai', openaiAdapter);
            this.healthStatus.set('openai', false);
            // Initialize Gemini adapter
            const geminiAdapter = new gemini_1.GeminiAdapter();
            this.adapters.set('gemini', geminiAdapter);
            this.healthStatus.set('gemini', false);
            logger_1.logger.info('All adapters initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize adapters', { error });
            throw error;
        }
    }
    initializeModelConfigs() {
        for (const model of config_1.serviceConfig.models) {
            this.modelConfigs.set(model.id, model);
        }
        logger_1.logger.info('Model configurations loaded', {
            modelCount: this.modelConfigs.size,
            models: Array.from(this.modelConfigs.keys()),
        });
    }
    startHealthChecks() {
        // Initial health check
        this.performHealthChecks();
        // Periodic health checks
        setInterval(() => {
            this.performHealthChecks();
        }, this.healthCheckInterval);
    }
    async performHealthChecks() {
        const promises = Array.from(this.adapters.entries()).map(async ([provider, adapter]) => {
            try {
                const isHealthy = await adapter.isHealthy();
                this.healthStatus.set(provider, isHealthy);
                this.lastHealthCheck.set(provider, Date.now());
                logger_1.logger.debug('Health check completed', {
                    provider,
                    healthy: isHealthy,
                });
            }
            catch (error) {
                this.healthStatus.set(provider, false);
                this.lastHealthCheck.set(provider, Date.now());
                logger_1.logger.warn('Health check failed', {
                    provider,
                    error: error.message,
                });
            }
        });
        await Promise.allSettled(promises);
    }
    getModelConfig(modelId) {
        return this.modelConfigs.get(modelId);
    }
    getAvailableModels() {
        return Array.from(this.modelConfigs.values()).filter(model => model.enabled);
    }
    getModelsByProvider(provider) {
        return this.getAvailableModels().filter(model => model.provider === provider);
    }
    isProviderHealthy(provider) {
        return this.healthStatus.get(provider) || false;
    }
    getProviderHealthStatus() {
        const status = {};
        for (const provider of this.adapters.keys()) {
            status[provider] = {
                healthy: this.healthStatus.get(provider) || false,
                lastCheck: this.lastHealthCheck.get(provider) || 0,
            };
        }
        return status;
    }
    async chatCompletion(request) {
        const modelConfig = this.getModelConfig(request.model);
        if (!modelConfig) {
            throw new types_1.ValidationError(`Model '${request.model}' not found`);
        }
        if (!modelConfig.enabled) {
            throw new types_1.ValidationError(`Model '${request.model}' is disabled`);
        }
        const adapter = this.getAdapter(modelConfig.provider);
        // Try primary model
        try {
            return await adapter.chatCompletion(request);
        }
        catch (error) {
            // Handle fallback if enabled
            if (config_1.ENV.FALLBACK_ENABLED && modelConfig.fallbackModels && modelConfig.fallbackModels.length > 0) {
                logger_1.logger.warn('Primary model failed, trying fallback', {
                    primaryModel: request.model,
                    fallbackModels: modelConfig.fallbackModels,
                    error: error.message,
                });
                return await this.tryFallbackModels(request, modelConfig.fallbackModels);
            }
            throw error;
        }
    }
    async *chatCompletionStream(request) {
        const modelConfig = this.getModelConfig(request.model);
        if (!modelConfig) {
            throw new types_1.ValidationError(`Model '${request.model}' not found`);
        }
        if (!modelConfig.enabled) {
            throw new types_1.ValidationError(`Model '${request.model}' is disabled`);
        }
        const adapter = this.getAdapter(modelConfig.provider);
        try {
            yield* adapter.chatCompletionStream(request);
        }
        catch (error) {
            // For streaming, we don't implement fallback as it's more complex
            // and would require buffering the entire response
            logger_1.logger.error('Streaming request failed', {
                model: request.model,
                provider: modelConfig.provider,
                error: error.message,
            });
            throw error;
        }
    }
    async tryFallbackModels(originalRequest, fallbackModels) {
        for (const fallbackModel of fallbackModels) {
            const fallbackConfig = this.getModelConfig(fallbackModel);
            if (!fallbackConfig || !fallbackConfig.enabled) {
                continue;
            }
            const fallbackAdapter = this.getAdapter(fallbackConfig.provider);
            if (!this.isProviderHealthy(fallbackConfig.provider)) {
                continue;
            }
            try {
                const fallbackRequest = { ...originalRequest, model: fallbackModel };
                const response = await fallbackAdapter.chatCompletion(fallbackRequest);
                logger_1.logger.info('Fallback model succeeded', {
                    originalModel: originalRequest.model,
                    fallbackModel,
                    provider: fallbackConfig.provider,
                });
                return response;
            }
            catch (error) {
                logger_1.logger.warn('Fallback model failed', {
                    fallbackModel,
                    provider: fallbackConfig.provider,
                    error: error.message,
                });
                continue;
            }
        }
        throw new types_1.ProviderError('All fallback models failed', 'openai');
    }
    getAdapter(provider) {
        const adapter = this.adapters.get(provider);
        if (!adapter) {
            throw new types_1.ValidationError(`Provider '${provider}' not supported`);
        }
        if (!this.isProviderHealthy(provider)) {
            throw new types_1.ProviderError(`Provider '${provider}' is currently unhealthy`, provider);
        }
        return adapter;
    }
    validateRequest(request) {
        const modelConfig = this.getModelConfig(request.model);
        if (!modelConfig) {
            throw new types_1.ValidationError(`Model '${request.model}' not found`);
        }
        const adapter = this.adapters.get(modelConfig.provider);
        if (!adapter) {
            throw new types_1.ValidationError(`Provider '${modelConfig.provider}' not supported`);
        }
        adapter.validateRequest(request);
    }
    getHealthStatus() {
        return {
            adapters: Array.from(this.adapters.keys()),
            health: this.getProviderHealthStatus(),
            models: {
                total: this.modelConfigs.size,
                enabled: this.getAvailableModels().length,
                byProvider: Object.fromEntries(Array.from(this.adapters.keys()).map(provider => [
                    provider,
                    this.getModelsByProvider(provider).length,
                ])),
            },
        };
    }
    async refreshHealth() {
        await this.performHealthChecks();
    }
    getModelInfo(modelId) {
        const config = this.getModelConfig(modelId);
        if (!config) {
            return null;
        }
        return {
            ...config,
            providerHealthy: this.isProviderHealthy(config.provider),
            lastHealthCheck: this.lastHealthCheck.get(config.provider),
        };
    }
    getAllModelsInfo() {
        return this.getAvailableModels().map(config => ({
            ...config,
            providerHealthy: this.isProviderHealthy(config.provider),
            lastHealthCheck: this.lastHealthCheck.get(config.provider),
        }));
    }
}
exports.AdapterManager = AdapterManager;
// Export singleton instance
exports.adapterManager = new AdapterManager();
//# sourceMappingURL=index.js.map