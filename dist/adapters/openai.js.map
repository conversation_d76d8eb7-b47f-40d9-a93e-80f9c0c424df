{"version": 3, "file": "openai.js", "sourceRoot": "", "sources": ["../../src/adapters/openai.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAC5B,mCAOiB;AACjB,qCAAyC;AACzC,2CAAyD;AACzD,mCAOiB;AAEjB,MAAa,aAAa;IACR,QAAQ,GAAG,QAAiB,CAAC;IACrC,MAAM,CAAU;IAChB,aAAa,GAAG,KAAK,CAAC;IAE9B;QACE,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,sBAAa,CAAC,SAAS,CAAC,MAAM,CAAC;YAE9C,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;gBACvB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,UAAU,EAAE,CAAC,EAAE,8BAA8B;aAC9C,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC/D,MAAM,IAAI,qBAAa,CAAC,qCAAqC,EAAE,QAAQ,EAAE,KAAc,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS;QACb,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAA,+BAAuB,GAAE,CAAC;YAE5C,wCAAwC;YACxC,MAAM,IAAA,mBAAW,EACf,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EACzB,IAAI,EACJ,6BAA6B,CAC9B,CAAC;YAEF,MAAM,QAAQ,GAAG,IAAA,yBAAiB,EAAC,SAAS,CAAC,CAAC;YAE9C,IAAA,wBAAe,EACb,IAAI,CAAC,QAAQ,EACb,cAAc,EACd,IAAA,yBAAiB,GAAE,EACnB,cAAc,EACd,QAAQ,EACR,IAAI,CACL,CAAC;YAEF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,eAAe,CAAC,OAA8B;QAC5C,IAAA,qCAA6B,EAAC,OAAO,CAAC,CAAC;QAEvC,8BAA8B;QAC9B,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC/B,MAAM,IAAI,uBAAe,CAAC,wCAAwC,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAA8B;QACjD,MAAM,SAAS,GAAG,IAAA,yBAAiB,GAAE,CAAC;QACtC,MAAM,SAAS,GAAG,IAAA,+BAAuB,GAAE,CAAC;QAE5C,IAAI,CAAC;YACH,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAE9B,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAErD,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAgB,EACrC,GAAG,EAAE,CAAC,IAAA,mBAAW,EACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,EAClD,sBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EACtC,wBAAwB,CACzB,EACD;gBACE,OAAO,EAAE,sBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU;gBAClD,eAAe,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE;oBACxC,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;wBAC3C,SAAS;wBACT,aAAa;wBACb,KAAK,EAAE,KAAK,CAAC,OAAO;wBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;qBACrB,CAAC,CAAC;gBACL,CAAC;aACF,CACF,CAAC;YAEF,MAAM,QAAQ,GAAG,IAAA,yBAAiB,EAAC,SAAS,CAAC,CAAC;YAC9C,MAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAsC,EAAE,QAAQ,CAAC,CAAC;YAErG,IAAA,wBAAe,EACb,IAAI,CAAC,QAAQ,EACb,OAAO,CAAC,KAAK,EACb,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACR,IAAI,CACL,CAAC;YAEF,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAA,yBAAiB,EAAC,SAAS,CAAC,CAAC;YAE9C,IAAA,wBAAe,EACb,IAAI,CAAC,QAAQ,EACb,OAAO,CAAC,KAAK,EACb,SAAS,EACT,iBAAiB,EACjB,QAAQ,EACR,KAAK,EACJ,KAAe,CAAC,OAAO,CACzB,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,KAAc,EAAE,SAAS,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED,KAAK,CAAA,CAAE,oBAAoB,CAAC,OAA8B;QACxD,MAAM,SAAS,GAAG,IAAA,yBAAiB,GAAE,CAAC;QACtC,MAAM,SAAS,GAAG,IAAA,+BAAuB,GAAE,CAAC;QAE5C,IAAI,CAAC;YACH,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAE9B,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAE3D,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAgB,EACnC,GAAG,EAAE,CAAC,IAAA,mBAAW,EACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,EAClD,sBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EACtC,+BAA+B,CAChC,EACD;gBACE,OAAO,EAAE,sBAAa,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU;gBAClD,eAAe,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE;oBACxC,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE;wBAClD,SAAS;wBACT,aAAa;wBACb,KAAK,EAAE,KAAK,CAAC,OAAO;wBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;qBACrB,CAAC,CAAC;gBACL,CAAC;aACF,CACF,CAAC;YAEF,IAAI,UAAU,GAAG,CAAC,CAAC;YAEnB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAa,EAAE,CAAC;gBACxC,UAAU,EAAE,CAAC;gBACb,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YACzC,CAAC;YAED,MAAM,QAAQ,GAAG,IAAA,yBAAiB,EAAC,SAAS,CAAC,CAAC;YAE9C,IAAA,wBAAe,EACb,IAAI,CAAC,QAAQ,EACb,OAAO,CAAC,KAAK,EACb,SAAS,EACT,wBAAwB,EACxB,QAAQ,EACR,IAAI,CACL,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,IAAA,yBAAiB,EAAC,SAAS,CAAC,CAAC;YAE9C,IAAA,wBAAe,EACb,IAAI,CAAC,QAAQ,EACb,OAAO,CAAC,KAAK,EACb,SAAS,EACT,wBAAwB,EACxB,QAAQ,EACR,KAAK,EACJ,KAAe,CAAC,OAAO,CACzB,CAAC;YAEF,MAAM,IAAI,CAAC,WAAW,CAAC,KAAc,EAAE,SAAS,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,OAA8B,EAAE,MAAM,GAAG,KAAK;QACrE,MAAM,aAAa,GAA2C;YAC5D,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACrC,IAAI,EAAE,GAAG,CAAC,IAAW;gBACrB,OAAO,EAAE,GAAG,CAAC,OAAO;gBACpB,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC;gBACnC,GAAG,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE,YAAY,EAAE,GAAG,CAAC,UAAU,EAAE,CAAC;aACxD,CAAC,CAAC;YACH,MAAM,EAAE,MAAa;SACtB,CAAC;QAEF,0BAA0B;QAC1B,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACtC,aAAa,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QAClD,CAAC;QAED,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACpC,aAAa,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC;QAC/C,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC/B,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;QACrC,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC/B,aAAa,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACpC,CAAC;QAED,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YAC1C,aAAa,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,CAAC;QAC3D,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC3C,aAAa,CAAC,iBAAiB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QAC7D,CAAC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC/B,aAAa,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACpC,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAChC,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QACtC,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACrC,aAAa,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC;QACjD,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,iBAAiB,CAAC,QAAoC,EAAE,cAAsB;QACpF,OAAO;YACL,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,MAAM,EAAE,QAAQ,CAAC,MAAM;YACvB,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACvC,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,OAAO,EAAE;oBACP,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,IAAW;oBAChC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;oBACrC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;iBACnF;gBACD,YAAY,EAAE,MAAM,CAAC,aAAa;aACnC,CAAC,CAAC;YACH,KAAK,EAAE;gBACL,YAAY,EAAE,QAAQ,CAAC,KAAK,EAAE,aAAa,IAAI,CAAC;gBAChD,gBAAgB,EAAE,QAAQ,CAAC,KAAK,EAAE,iBAAiB,IAAI,CAAC;gBACxD,WAAW,EAAE,QAAQ,CAAC,KAAK,EAAE,YAAY,IAAI,CAAC;aAC/C;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc;SACf,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,KAAsC;QACjE,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACpC,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,KAAK,EAAE;oBACL,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAW;oBAC9B,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE;oBACnC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;iBAChH;gBACD,YAAY,EAAE,MAAM,CAAC,aAAa;aACnC,CAAC,CAAC;YACH,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;IACJ,CAAC;IAEO,WAAW,CAAC,KAAY,EAAE,SAAiB;QACjD,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;QAE3D,gCAAgC;QAChC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,OAAO,IAAI,qBAAa,CAAC,wBAAwB,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACzC,OAAO,IAAI,qBAAa,CAAC,4BAA4B,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,qBAAa,CAAC,wBAAwB,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,qBAAa,CAAC,wBAAwB,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC3E,CAAC;QAED,OAAO,IAAI,qBAAa,CAAC,iBAAiB,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACnF,CAAC;CACF;AArTD,sCAqTC"}