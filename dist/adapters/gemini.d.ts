import { AIProviderAdapter, ChatCompletionRequest, ChatCompletionResponse, StreamChunk } from '../types';
export declare class GeminiA<PERSON>pter implements AIProviderAdapter {
    readonly provider: "gemini";
    private client;
    private isInitialized;
    constructor();
    private initializeClient;
    isHealthy(): Promise<boolean>;
    validateRequest(request: ChatCompletionRequest): void;
    chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
    chatCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk>;
    private transformRequest;
    private convertMessagesToContents;
    private transformResponse;
    private transformStreamChunk;
    private mapFinishReason;
    private handleError;
}
//# sourceMappingURL=gemini.d.ts.map