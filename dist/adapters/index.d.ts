import { OpenAIAdapter } from './openai';
import { GeminiAdapter } from './gemini';
import { AIProvider, ChatCompletionRequest, ChatCompletionResponse, StreamChunk, ModelConfig } from '@/types';
export declare class AdapterManager {
    private adapters;
    private modelConfigs;
    private healthStatus;
    private lastHealthCheck;
    private readonly healthCheckInterval;
    constructor();
    private initializeAdapters;
    private initializeModelConfigs;
    private startHealthChecks;
    private performHealthChecks;
    getModelConfig(modelId: string): ModelConfig | undefined;
    getAvailableModels(): ModelConfig[];
    getModelsByProvider(provider: AIProvider): ModelConfig[];
    isProviderHealthy(provider: AIProvider): boolean;
    getProviderHealthStatus(): Record<AIProvider, {
        healthy: boolean;
        lastCheck: number;
    }>;
    chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
    chatCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk>;
    private tryFallbackModels;
    private getAdapter;
    validateRequest(request: ChatCompletionRequest): void;
    getHealthStatus(): {
        adapters: AIProvider[];
        health: Record<AIProvider, {
            healthy: boolean;
            lastCheck: number;
        }>;
        models: {
            total: number;
            enabled: number;
            byProvider: {
                [k: string]: number;
            };
        };
    };
    refreshHealth(): Promise<void>;
    getModelInfo(modelId: string): {
        providerHealthy: boolean;
        lastHealthCheck: number | undefined;
        id: string;
        name: string;
        provider: AIProvider;
        endpoint?: string;
        authMethod: import("@/types").AuthMethod;
        maxTokens?: number;
        temperature?: number;
        topP?: number;
        topK?: number;
        enabled: boolean;
        fallbackModels?: string[];
        rateLimits?: {
            requestsPerMinute: number;
            tokensPerMinute: number;
        };
    } | null;
    getAllModelsInfo(): {
        providerHealthy: boolean;
        lastHealthCheck: number | undefined;
        id: string;
        name: string;
        provider: AIProvider;
        endpoint?: string;
        authMethod: import("@/types").AuthMethod;
        maxTokens?: number;
        temperature?: number;
        topP?: number;
        topK?: number;
        enabled: boolean;
        fallbackModels?: string[];
        rateLimits?: {
            requestsPerMinute: number;
            tokensPerMinute: number;
        };
    }[];
}
export declare const adapterManager: AdapterManager;
export { OpenAIAdapter, GeminiAdapter };
//# sourceMappingURL=index.d.ts.map