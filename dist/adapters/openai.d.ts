import { AIProviderAdapter, ChatCompletionRequest, ChatCompletionResponse, StreamChunk } from '../types';
export declare class OpenAIAdapter implements AIProviderAdapter {
    readonly provider: "openai";
    private client;
    private isInitialized;
    constructor();
    private initializeClient;
    isHealthy(): Promise<boolean>;
    validateRequest(request: ChatCompletionRequest): void;
    chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
    chatCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk>;
    private transformRequest;
    private transformResponse;
    private transformStreamChunk;
    private handleError;
}
//# sourceMappingURL=openai.d.ts.map