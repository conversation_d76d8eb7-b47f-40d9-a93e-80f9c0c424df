{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/adapters/index.ts"], "names": [], "mappings": ";;;AAAA,qCAAyC;AAuShC,8FAvSA,sBAAa,OAuSA;AAtStB,qCAAyC;AAsSjB,8FAtSf,sBAAa,OAsSe;AArSrC,mCASiB;AACjB,qCAA8C;AAC9C,2CAAwC;AAGxC,MAAa,cAAc;IACjB,QAAQ,GAAuC,IAAI,GAAG,EAAE,CAAC;IACzD,YAAY,GAA6B,IAAI,GAAG,EAAE,CAAC;IACnD,YAAY,GAA6B,IAAI,GAAG,EAAE,CAAC;IACnD,eAAe,GAA4B,IAAI,GAAG,EAAE,CAAC;IAC5C,mBAAmB,GAAG,KAAK,CAAC,CAAC,WAAW;IAEzD;QACE,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEO,kBAAkB;QACxB,IAAI,CAAC;YACH,4BAA4B;YAC5B,MAAM,aAAa,GAAG,IAAI,sBAAa,EAAE,CAAC;YAC1C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC3C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEvC,4BAA4B;YAC5B,MAAM,aAAa,GAAG,IAAI,sBAAa,EAAE,CAAC;YAC1C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;YAC3C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEvC,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,sBAAsB;QAC5B,KAAK,MAAM,KAAK,IAAI,sBAAa,CAAC,MAAM,EAAE,CAAC;YACzC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACzC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YAClC,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SAC7C,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB;QACvB,uBAAuB;QACvB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,yBAAyB;QACzB,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAC/B,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,EAAE;YACrF,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,SAAS,EAAE,CAAC;gBAC5C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;gBAC3C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBAE/C,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;oBACrC,QAAQ;oBACR,OAAO,EAAE,SAAS;iBACnB,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBACvC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBAE/C,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;oBACjC,QAAQ;oBACR,KAAK,EAAG,KAAe,CAAC,OAAO;iBAChC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAEM,cAAc,CAAC,OAAe;QACnC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAEM,kBAAkB;QACvB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/E,CAAC;IAEM,mBAAmB,CAAC,QAAoB;QAC7C,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;IAChF,CAAC;IAEM,iBAAiB,CAAC,QAAoB;QAC3C,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;IAClD,CAAC;IAEM,uBAAuB;QAC5B,MAAM,MAAM,GAAQ,EAAE,CAAC;QAEvB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;YAC5C,MAAM,CAAC,QAAQ,CAAC,GAAG;gBACjB,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK;gBACjD,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;aACnD,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,OAA8B;QACxD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,uBAAe,CAAC,UAAU,OAAO,CAAC,KAAK,aAAa,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,uBAAe,CAAC,UAAU,OAAO,CAAC,KAAK,eAAe,CAAC,CAAC;QACpE,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEtD,oBAAoB;QACpB,IAAI,CAAC;YACH,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,6BAA6B;YAC7B,IAAI,YAAG,CAAC,gBAAgB,IAAI,WAAW,CAAC,cAAc,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChG,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;oBACnD,YAAY,EAAE,OAAO,CAAC,KAAK;oBAC3B,cAAc,EAAE,WAAW,CAAC,cAAc;oBAC1C,KAAK,EAAG,KAAe,CAAC,OAAO;iBAChC,CAAC,CAAC;gBAEH,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,CAAC,cAAc,CAAC,CAAC;YAC3E,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEM,KAAK,CAAA,CAAE,oBAAoB,CAAC,OAA8B;QAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,uBAAe,CAAC,UAAU,OAAO,CAAC,KAAK,aAAa,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,uBAAe,CAAC,UAAU,OAAO,CAAC,KAAK,eAAe,CAAC,CAAC;QACpE,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEtD,IAAI,CAAC;YACH,KAAK,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,kEAAkE;YAClE,kDAAkD;YAClD,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBACvC,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,eAAsC,EACtC,cAAwB;QAExB,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;YAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC1D,IAAI,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC/C,SAAS;YACX,CAAC;YAED,MAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACjE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrD,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,eAAe,GAAG,EAAE,GAAG,eAAe,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;gBACrE,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;gBAEvE,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;oBACtC,aAAa,EAAE,eAAe,CAAC,KAAK;oBACpC,aAAa;oBACb,QAAQ,EAAE,cAAc,CAAC,QAAQ;iBAClC,CAAC,CAAC;gBAEH,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;oBACnC,aAAa;oBACb,QAAQ,EAAE,cAAc,CAAC,QAAQ;oBACjC,KAAK,EAAG,KAAe,CAAC,OAAO;iBAChC,CAAC,CAAC;gBAEH,SAAS;YACX,CAAC;QACH,CAAC;QAED,MAAM,IAAI,qBAAa,CAAC,4BAA4B,EAAE,QAAQ,CAAC,CAAC;IAClE,CAAC;IAEO,UAAU,CAAC,QAAoB;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,uBAAe,CAAC,aAAa,QAAQ,iBAAiB,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,qBAAa,CAAC,aAAa,QAAQ,0BAA0B,EAAE,QAAQ,CAAC,CAAC;QACrF,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,eAAe,CAAC,OAA8B;QACnD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,uBAAe,CAAC,UAAU,OAAO,CAAC,KAAK,aAAa,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,uBAAe,CAAC,aAAa,WAAW,CAAC,QAAQ,iBAAiB,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAEM,eAAe;QACpB,OAAO;YACL,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC1C,MAAM,EAAE,IAAI,CAAC,uBAAuB,EAAE;YACtC,MAAM,EAAE;gBACN,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;gBAC7B,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,MAAM;gBACzC,UAAU,EAAE,MAAM,CAAC,WAAW,CAC5B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC/C,QAAQ;oBACR,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,MAAM;iBAC1C,CAAC,CACH;aACF;SACF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,aAAa;QACxB,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACnC,CAAC;IAEM,YAAY,CAAC,OAAe;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,GAAG,MAAM;YACT,eAAe,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC;YACxD,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC;SAC3D,CAAC;IACJ,CAAC;IAEM,gBAAgB;QACrB,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC9C,GAAG,MAAM;YACT,eAAe,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC;YACxD,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC;SAC3D,CAAC,CAAC,CAAC;IACN,CAAC;CACF;AAjRD,wCAiRC;AAED,4BAA4B;AACf,QAAA,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC"}