"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIAdapter = void 0;
const openai_1 = __importDefault(require("openai"));
const types_1 = require("../types");
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
const utils_1 = require("../utils");
class OpenAIAdapter {
    provider = 'openai';
    client;
    isInitialized = false;
    constructor() {
        this.initializeClient();
    }
    initializeClient() {
        try {
            const config = config_1.serviceConfig.providers.openai;
            this.client = new openai_1.default({
                apiKey: config.apiKey,
                baseURL: config.baseURL,
                timeout: config.timeout,
                maxRetries: 0, // We handle retries ourselves
            });
            this.isInitialized = true;
            logger_1.logger.info('OpenAI adapter initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize OpenAI adapter', { error });
            throw new types_1.ProviderError('Failed to initialize OpenAI adapter', 'openai', error);
        }
    }
    async isHealthy() {
        if (!this.isInitialized) {
            return false;
        }
        try {
            const startTime = (0, utils_1.getPerformanceTimestamp)();
            // Simple health check using models list
            await (0, utils_1.withTimeout)(this.client.models.list(), 5000, 'OpenAI health check timeout');
            const duration = (0, utils_1.calculateDuration)(startTime);
            (0, logger_1.logProviderCall)(this.provider, 'health-check', (0, utils_1.generateRequestId)(), 'health-check', duration, true);
            return true;
        }
        catch (error) {
            logger_1.logger.warn('OpenAI health check failed', { error });
            return false;
        }
    }
    validateRequest(request) {
        (0, utils_1.validateChatCompletionRequest)(request);
        // OpenAI-specific validations
        if (request.topK !== undefined) {
            throw new types_1.ValidationError('OpenAI does not support topK parameter');
        }
    }
    async chatCompletion(request) {
        const requestId = (0, utils_1.generateRequestId)();
        const startTime = (0, utils_1.getPerformanceTimestamp)();
        try {
            this.validateRequest(request);
            const openaiRequest = this.transformRequest(request);
            const response = await (0, utils_1.retryWithBackoff)(() => (0, utils_1.withTimeout)(this.client.chat.completions.create(openaiRequest), config_1.serviceConfig.providers.openai.timeout, 'OpenAI request timeout'), {
                retries: config_1.serviceConfig.providers.openai.maxRetries,
                onFailedAttempt: (error, attemptNumber) => {
                    logger_1.logger.warn('OpenAI request attempt failed', {
                        requestId,
                        attemptNumber,
                        error: error.message,
                        model: request.model,
                    });
                },
            });
            const duration = (0, utils_1.calculateDuration)(startTime);
            const transformedResponse = this.transformResponse(response, duration);
            (0, logger_1.logProviderCall)(this.provider, request.model, requestId, 'chat-completion', duration, true);
            return transformedResponse;
        }
        catch (error) {
            const duration = (0, utils_1.calculateDuration)(startTime);
            (0, logger_1.logProviderCall)(this.provider, request.model, requestId, 'chat-completion', duration, false, error.message);
            throw this.handleError(error, requestId);
        }
    }
    async *chatCompletionStream(request) {
        const requestId = (0, utils_1.generateRequestId)();
        const startTime = (0, utils_1.getPerformanceTimestamp)();
        try {
            this.validateRequest(request);
            const openaiRequest = this.transformRequest(request, true);
            const stream = await (0, utils_1.retryWithBackoff)(() => (0, utils_1.withTimeout)(this.client.chat.completions.create(openaiRequest), config_1.serviceConfig.providers.openai.timeout, 'OpenAI stream request timeout'), {
                retries: config_1.serviceConfig.providers.openai.maxRetries,
                onFailedAttempt: (error, attemptNumber) => {
                    logger_1.logger.warn('OpenAI stream request attempt failed', {
                        requestId,
                        attemptNumber,
                        error: error.message,
                        model: request.model,
                    });
                },
            });
            let chunkCount = 0;
            for await (const chunk of stream) {
                chunkCount++;
                yield this.transformStreamChunk(chunk);
            }
            const duration = (0, utils_1.calculateDuration)(startTime);
            (0, logger_1.logProviderCall)(this.provider, request.model, requestId, 'chat-completion-stream', duration, true);
        }
        catch (error) {
            const duration = (0, utils_1.calculateDuration)(startTime);
            (0, logger_1.logProviderCall)(this.provider, request.model, requestId, 'chat-completion-stream', duration, false, error.message);
            throw this.handleError(error, requestId);
        }
    }
    transformRequest(request, stream = false) {
        const openaiRequest = {
            model: request.model,
            messages: request.messages.map(msg => ({
                role: msg.role,
                content: msg.content,
                ...(msg.name && { name: msg.name }),
                ...(msg.toolCallId && { tool_call_id: msg.toolCallId }),
            })),
            stream: stream,
        };
        // Add optional parameters
        if (request.temperature !== undefined) {
            openaiRequest.temperature = request.temperature;
        }
        if (request.maxTokens !== undefined) {
            openaiRequest.max_tokens = request.maxTokens;
        }
        if (request.topP !== undefined) {
            openaiRequest.top_p = request.topP;
        }
        if (request.stop !== undefined) {
            openaiRequest.stop = request.stop;
        }
        if (request.presencePenalty !== undefined) {
            openaiRequest.presence_penalty = request.presencePenalty;
        }
        if (request.frequencyPenalty !== undefined) {
            openaiRequest.frequency_penalty = request.frequencyPenalty;
        }
        if (request.user !== undefined) {
            openaiRequest.user = request.user;
        }
        if (request.tools !== undefined) {
            openaiRequest.tools = request.tools;
        }
        if (request.toolChoice !== undefined) {
            openaiRequest.tool_choice = request.toolChoice;
        }
        return openaiRequest;
    }
    transformResponse(response, processingTime) {
        return {
            id: response.id,
            object: response.object,
            created: response.created,
            model: response.model,
            choices: response.choices.map(choice => ({
                index: choice.index,
                message: {
                    role: choice.message.role,
                    content: choice.message.content || '',
                    ...(choice.message.tool_calls && choice.message.tool_calls[0]?.id && { toolCallId: choice.message.tool_calls[0].id }),
                },
                finishReason: choice.finish_reason,
            })),
            usage: {
                promptTokens: response.usage?.prompt_tokens || 0,
                completionTokens: response.usage?.completion_tokens || 0,
                totalTokens: response.usage?.total_tokens || 0,
            },
            provider: this.provider,
            processingTime,
        };
    }
    transformStreamChunk(chunk) {
        return {
            id: chunk.id,
            object: chunk.object,
            created: chunk.created,
            model: chunk.model,
            choices: chunk.choices.map(choice => ({
                index: choice.index,
                delta: {
                    role: choice.delta.role,
                    content: choice.delta.content || '',
                    ...(choice.delta.tool_calls && choice.delta.tool_calls[0]?.id && { toolCallId: choice.delta.tool_calls[0].id }),
                },
                finishReason: choice.finish_reason,
            })),
            provider: this.provider,
        };
    }
    handleError(error, requestId) {
        logger_1.logger.error('OpenAI adapter error', { error, requestId });
        // Handle specific OpenAI errors
        if (error.message.includes('timeout')) {
            return new types_1.ProviderError('OpenAI request timeout', this.provider, error);
        }
        if (error.message.includes('rate limit')) {
            return new types_1.ProviderError('OpenAI rate limit exceeded', this.provider, error);
        }
        if (error.message.includes('invalid_api_key')) {
            return new types_1.ProviderError('Invalid OpenAI API key', this.provider, error);
        }
        if (error.message.includes('model_not_found')) {
            return new types_1.ProviderError('OpenAI model not found', this.provider, error);
        }
        return new types_1.ProviderError(`OpenAI error: ${error.message}`, this.provider, error);
    }
}
exports.OpenAIAdapter = OpenAIAdapter;
//# sourceMappingURL=openai.js.map