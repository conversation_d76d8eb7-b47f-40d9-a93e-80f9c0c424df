import { v4 as uuidv4 } from 'uuid';
import { performance } from 'perf_hooks';
import pRetry from 'p-retry';
import pTimeout from 'p-timeout';
import { ChatCompletionRequest, ValidationError, AIProvider } from '@/types';
import { ENV } from '@/config';

/**
 * Generate a unique request ID
 */
export const generateRequestId = (): string => {
  return uuidv4();
};

/**
 * Get current timestamp in milliseconds
 */
export const getCurrentTimestamp = (): number => {
  return Date.now();
};

/**
 * Get high-resolution timestamp for performance measurement
 */
export const getPerformanceTimestamp = (): number => {
  return performance.now();
};

/**
 * Calculate duration between two performance timestamps
 */
export const calculateDuration = (startTime: number, endTime?: number): number => {
  const end = endTime || performance.now();
  return Math.round((end - startTime) * 100) / 100; // Round to 2 decimal places
};

/**
 * Sleep for specified milliseconds
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Retry function with exponential backoff
 */
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  options: {
    retries?: number;
    minTimeout?: number;
    maxTimeout?: number;
    factor?: number;
    onFailedAttempt?: (error: any, attemptNumber: number) => void;
  } = {}
): Promise<T> => {
  const {
    retries = ENV.MAX_RETRIES,
    minTimeout = ENV.RETRY_DELAY,
    maxTimeout = 30000,
    factor = 2,
    onFailedAttempt,
  } = options;

  return pRetry(fn, {
    retries,
    minTimeout,
    maxTimeout,
    factor,
    onFailedAttempt: (error) => {
      if (onFailedAttempt) {
        onFailedAttempt(error, error.attemptNumber);
      }
    },
  });
};

/**
 * Add timeout to a promise
 */
export const withTimeout = async <T>(
  promise: Promise<T>,
  timeoutMs: number = ENV.REQUEST_TIMEOUT,
  errorMessage: string = 'Operation timed out'
): Promise<T> => {
  return pTimeout(promise, {
    milliseconds: timeoutMs,
    message: errorMessage,
  });
};

/**
 * Validate chat completion request
 */
export const validateChatCompletionRequest = (request: ChatCompletionRequest): void => {
  if (!request.model) {
    throw new ValidationError('Model is required');
  }

  if (!request.messages || !Array.isArray(request.messages) || request.messages.length === 0) {
    throw new ValidationError('Messages array is required and must not be empty');
  }

  // Validate messages
  for (const [index, message] of request.messages.entries()) {
    if (!message.role || !['system', 'user', 'assistant', 'tool'].includes(message.role)) {
      throw new ValidationError(`Invalid role at message index ${index}`);
    }

    if (!message.content || typeof message.content !== 'string') {
      throw new ValidationError(`Invalid content at message index ${index}`);
    }
  }

  // Validate optional parameters
  if (request.temperature !== undefined) {
    if (typeof request.temperature !== 'number' || request.temperature < 0 || request.temperature > 2) {
      throw new ValidationError('Temperature must be a number between 0 and 2');
    }
  }

  if (request.maxTokens !== undefined) {
    if (typeof request.maxTokens !== 'number' || request.maxTokens < 1) {
      throw new ValidationError('Max tokens must be a positive number');
    }
  }

  if (request.topP !== undefined) {
    if (typeof request.topP !== 'number' || request.topP < 0 || request.topP > 1) {
      throw new ValidationError('Top P must be a number between 0 and 1');
    }
  }

  if (request.topK !== undefined) {
    if (typeof request.topK !== 'number' || request.topK < 1) {
      throw new ValidationError('Top K must be a positive number');
    }
  }
};

/**
 * Sanitize error message for client response
 */
export const sanitizeErrorMessage = (error: Error, includeStack: boolean = false): any => {
  const sanitized: any = {
    name: error.name,
    message: error.message,
  };

  if (includeStack && ENV.NODE_ENV === 'development') {
    sanitized.stack = error.stack;
  }

  return sanitized;
};

/**
 * Extract provider from model name
 */
export const extractProviderFromModel = (modelName: string): AIProvider | null => {
  const lowerModel = modelName.toLowerCase();
  
  if (lowerModel.includes('gpt') || lowerModel.includes('openai')) {
    return 'openai';
  }
  
  if (lowerModel.includes('gemini') || lowerModel.includes('google')) {
    return 'gemini';
  }
  
  return null;
};

/**
 * Format bytes to human readable string
 */
export const formatBytes = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * Get memory usage information
 */
export const getMemoryUsage = () => {
  const usage = process.memoryUsage();
  return {
    rss: formatBytes(usage.rss),
    heapTotal: formatBytes(usage.heapTotal),
    heapUsed: formatBytes(usage.heapUsed),
    external: formatBytes(usage.external),
    arrayBuffers: formatBytes(usage.arrayBuffers),
  };
};

/**
 * Get system uptime in seconds
 */
export const getUptime = (): number => {
  return Math.floor(process.uptime());
};

/**
 * Deep clone an object
 */
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

/**
 * Check if a value is empty (null, undefined, empty string, empty array, empty object)
 */
export const isEmpty = (value: any): boolean => {
  if (value == null) return true;
  if (typeof value === 'string') return value.trim().length === 0;
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

/**
 * Truncate string to specified length
 */
export const truncateString = (str: string, maxLength: number, suffix: string = '...'): string => {
  if (str.length <= maxLength) return str;
  return str.substring(0, maxLength - suffix.length) + suffix;
};

/**
 * Parse JSON safely
 */
export const safeJsonParse = <T = any>(jsonString: string, defaultValue: T | null = null): T | null => {
  try {
    return JSON.parse(jsonString);
  } catch {
    return defaultValue;
  }
};

/**
 * Create a debounced function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Create a throttled function
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
