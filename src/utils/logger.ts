import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import { ENV } from '@/config';

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS',
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const logEntry = {
      timestamp,
      level,
      message,
      ...meta,
    };
    
    // Add request context if available
    if (meta['requestId']) {
      (logEntry as any).requestId = meta['requestId'];
    }

    if (meta['provider']) {
      (logEntry as any).provider = meta['provider'];
    }

    if (meta['model']) {
      (logEntry as any).model = meta['model'];
    }
    
    return JSON.stringify(logEntry);
  })
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss.SSS',
  }),
  winston.format.printf(({ timestamp, level, message, requestId, provider, model, ...meta }) => {
    let logMessage = `${timestamp} [${level}] ${message}`;
    
    if (requestId) {
      logMessage += ` [req:${requestId}]`;
    }
    
    if (provider) {
      logMessage += ` [${provider}]`;
    }
    
    if (model) {
      logMessage += ` [${model}]`;
    }
    
    // Add additional metadata if present
    const metaKeys = Object.keys(meta);
    if (metaKeys.length > 0) {
      const metaString = metaKeys
        .map(key => `${key}:${meta[key]}`)
        .join(' ');
      logMessage += ` {${metaString}}`;
    }
    
    return logMessage;
  })
);

// Create transports
const transports: winston.transport[] = [];

// Console transport for development
if (ENV.NODE_ENV === 'development') {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
      level: ENV.LOG_LEVEL,
    })
  );
} else {
  // JSON console output for production
  transports.push(
    new winston.transports.Console({
      format: logFormat,
      level: ENV.LOG_LEVEL,
    })
  );
}

// File transports for production
if (ENV.NODE_ENV === 'production') {
  // Error logs
  transports.push(
    new DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      format: logFormat,
      maxSize: '20m',
      maxFiles: '14d',
      zippedArchive: true,
    })
  );
  
  // Combined logs
  transports.push(
    new DailyRotateFile({
      filename: 'logs/combined-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      format: logFormat,
      maxSize: '20m',
      maxFiles: '7d',
      zippedArchive: true,
    })
  );
  
  // Access logs
  transports.push(
    new DailyRotateFile({
      filename: 'logs/access-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'info',
      format: logFormat,
      maxSize: '20m',
      maxFiles: '7d',
      zippedArchive: true,
    })
  );
}

// Create logger instance
export const logger = winston.createLogger({
  level: ENV.LOG_LEVEL,
  format: logFormat,
  transports,
  exitOnError: false,
  // Handle uncaught exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({ filename: 'logs/exceptions.log' }),
  ],
  rejectionHandlers: [
    new winston.transports.File({ filename: 'logs/rejections.log' }),
  ],
});

// Create specialized loggers
export const accessLogger = logger.child({ component: 'access' });
export const errorLogger = logger.child({ component: 'error' });
export const providerLogger = logger.child({ component: 'provider' });
export const metricsLogger = logger.child({ component: 'metrics' });

// Helper functions for structured logging
export const logRequest = (requestId: string, method: string, url: string, userAgent?: string) => {
  accessLogger.info('Request received', {
    requestId,
    method,
    url,
    userAgent,
  });
};

export const logResponse = (
  requestId: string,
  statusCode: number,
  duration: number,
  provider?: string,
  model?: string
) => {
  accessLogger.info('Request completed', {
    requestId,
    statusCode,
    duration,
    provider,
    model,
  });
};

export const logError = (
  error: Error,
  requestId?: string,
  provider?: string,
  model?: string,
  context?: Record<string, any>
) => {
  errorLogger.error('Error occurred', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    requestId,
    provider,
    model,
    ...context,
  });
};

export const logProviderCall = (
  provider: string,
  model: string,
  requestId: string,
  action: string,
  duration?: number,
  success?: boolean,
  error?: string
) => {
  providerLogger.info('Provider call', {
    provider,
    model,
    requestId,
    action,
    duration,
    success,
    error,
  });
};

export const logMetrics = (metrics: Record<string, any>) => {
  metricsLogger.info('Metrics', metrics);
};

// Export default logger
export default logger;
