import { FastifyRequest, FastifyReply } from 'fastify';

// AI Provider Types
export type AIProvider = 'openai' | 'gemini';

export type AuthMethod = 'bearer' | 'google-auth' | 'api-key';

// Model Configuration
export interface ModelConfig {
  id: string;
  name: string;
  provider: AIProvider;
  endpoint?: string;
  authMethod: AuthMethod;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  topK?: number;
  enabled: boolean;
  fallbackModels?: string[];
  rateLimits?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
}

// Request/Response Types
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  name?: string;
  toolCallId?: string;
}

export interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  stream?: boolean;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  topK?: number;
  stop?: string | string[];
  presencePenalty?: number;
  frequencyPenalty?: number;
  user?: string;
  tools?: any[];
  toolChoice?: any;
}

export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: ChatMessage;
    finishReason: string | null;
  }>;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  provider: AIProvider;
  processingTime: number;
}

export interface StreamChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    delta: Partial<ChatMessage>;
    finishReason: string | null;
  }>;
  provider: AIProvider;
}

// Provider Adapter Interface
export interface AIProviderAdapter {
  provider: AIProvider;
  isHealthy(): Promise<boolean>;
  chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse>;
  chatCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk>;
  validateRequest(request: ChatCompletionRequest): void;
}

// Service Configuration
export interface ServiceConfig {
  server: {
    port: number;
    host: string;
    logLevel: string;
  };
  providers: {
    openai: {
      apiKey: string;
      baseURL?: string;
      timeout: number;
      maxRetries: number;
    };
    gemini: {
      apiKey: string;
      project?: string;
      location?: string;
      authMethod: AuthMethod;
      timeout: number;
      maxRetries: number;
    };
  };
  models: ModelConfig[];
  rateLimit: {
    max: number;
    timeWindow: number;
  };
  security: {
    corsOrigin: string;
    helmetEnabled: boolean;
  };
  monitoring: {
    healthCheckEnabled: boolean;
    metricsEnabled: boolean;
  };
}

// Error Types
export class AIProxyError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public provider?: AIProvider,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'AIProxyError';
  }
}

export class ValidationError extends AIProxyError {
  constructor(message: string, _field?: string) {
    super(message, 400);
    this.name = 'ValidationError';
  }
}

export class ProviderError extends AIProxyError {
  constructor(message: string, provider: AIProvider, originalError?: Error) {
    super(message, 502, provider, originalError);
    this.name = 'ProviderError';
  }
}

export class RateLimitError extends AIProxyError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429);
    this.name = 'RateLimitError';
  }
}

// Fastify Extensions
declare module 'fastify' {
  interface FastifyInstance {
    config: ServiceConfig;
  }
}

// Request/Reply Extensions
export interface TypedFastifyRequest<T = any> extends FastifyRequest {
  body: T;
}

export interface TypedFastifyReply extends FastifyReply {
  sse: (data: any) => void;
}

// Metrics Types
export interface RequestMetrics {
  requestId: string;
  provider: AIProvider;
  model: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  tokenUsage?: {
    prompt: number;
    completion: number;
    total: number;
  };
  success: boolean;
  error?: string;
}

// Health Check Types
export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  uptime: number;
  providers: Record<AIProvider, {
    status: 'healthy' | 'unhealthy';
    latency?: number;
    lastCheck: string;
  }>;
  system: {
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    cpu: {
      usage: number;
    };
  };
}
