import { FastifyRequest, FastifyReply, FastifyInstance } from 'fastify';
import { generateRequestId, getPerformanceTimestamp } from '@/utils';
import { logger, logRequest, logError } from '@/utils/logger';
import { AIProxyError, ValidationError, ProviderError, RateLimitError } from '@/types';

/**
 * Request ID middleware - adds unique request ID to each request
 */
export const requestIdMiddleware = async (request: FastifyRequest, reply: FastifyReply) => {
  const requestId = generateRequestId();
  request.headers['x-request-id'] = requestId;
  reply.header('x-request-id', requestId);
  
  // Store request ID for logging
  (request as any).requestId = requestId;
};

/**
 * Request logging middleware
 */
export const requestLoggingMiddleware = async (request: FastifyRequest, _reply: FastifyReply) => {
  const startTime = getPerformanceTimestamp();
  const requestId = (request as any).requestId || 'unknown';
  
  // Log incoming request
  logRequest(
    requestId,
    request.method,
    request.url,
    request.headers['user-agent']
  );
  

  
  // Store start time for response logging in request context
  (request as any).startTime = startTime;
};

/**
 * Error handling middleware
 */
export const errorHandlerMiddleware = (
  error: Error,
  request: FastifyRequest,
  reply: FastifyReply
) => {
  const requestId = (request as any).requestId || 'unknown';
  
  // Log the error
  logError(error, requestId);
  
  // Handle different error types
  if (error instanceof ValidationError) {
    return reply.status(400).send({
      error: {
        type: 'validation_error',
        message: error.message,
        code: 'VALIDATION_ERROR',
      },
      requestId,
    });
  }
  
  if (error instanceof RateLimitError) {
    return reply.status(429).send({
      error: {
        type: 'rate_limit_error',
        message: error.message,
        code: 'RATE_LIMIT_EXCEEDED',
      },
      requestId,
    });
  }
  
  if (error instanceof ProviderError) {
    return reply.status(error.statusCode).send({
      error: {
        type: 'provider_error',
        message: error.message,
        provider: error.provider,
        code: 'PROVIDER_ERROR',
      },
      requestId,
    });
  }
  
  if (error instanceof AIProxyError) {
    return reply.status(error.statusCode).send({
      error: {
        type: 'ai_proxy_error',
        message: error.message,
        code: 'AI_PROXY_ERROR',
      },
      requestId,
    });
  }
  
  // Handle Fastify validation errors
  if (error.name === 'FastifyError' && (error as any).validation) {
    return reply.status(400).send({
      error: {
        type: 'validation_error',
        message: 'Request validation failed',
        details: (error as any).validation,
        code: 'REQUEST_VALIDATION_ERROR',
      },
      requestId,
    });
  }
  
  // Handle timeout errors
  if (error.message.includes('timeout') || error.name === 'TimeoutError') {
    return reply.status(408).send({
      error: {
        type: 'timeout_error',
        message: 'Request timeout',
        code: 'REQUEST_TIMEOUT',
      },
      requestId,
    });
  }
  
  // Handle unknown errors
  logger.error('Unhandled error', {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    requestId,
    url: request.url,
    method: request.method,
  });
  
  return reply.status(500).send({
    error: {
      type: 'internal_error',
      message: 'Internal server error',
      code: 'INTERNAL_ERROR',
    },
    requestId,
  });
};

/**
 * CORS middleware configuration
 */
export const corsMiddleware = {
  origin: (origin: string, callback: (err: Error | null, allow?: boolean) => void) => {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    // In production, you should validate against allowed origins
    // For now, we'll allow all origins as configured
    return callback(null, true);
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'X-Request-ID',
    'Accept',
    'Origin',
  ],
  exposedHeaders: ['X-Request-ID'],
};

/**
 * Security headers middleware
 */
export const securityMiddleware = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for API server
  crossOriginOpenerPolicy: false,   // Disable for API server
  crossOriginResourcePolicy: false, // Disable for API server
  dnsPrefetchControl: true,
  frameguard: { action: 'deny' },
  hidePoweredBy: true,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true,
  },
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: false,
  referrerPolicy: 'no-referrer',
  xssFilter: true,
};

/**
 * Rate limiting configuration
 */
export const rateLimitMiddleware = {
  max: 100, // requests per timeWindow
  timeWindow: '1 minute',
  skipOnError: true,
  skipSuccessfulRequests: false,
  keyGenerator: (request: FastifyRequest) => {
    // Use IP address as the key
    return request.ip;
  },
  errorResponseBuilder: (request: FastifyRequest, context: any) => {
    const requestId = (request as any).requestId || 'unknown';
    
    return {
      error: {
        type: 'rate_limit_error',
        message: `Rate limit exceeded. Try again in ${Math.round(context.ttl / 1000)} seconds.`,
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.round(context.ttl / 1000),
      },
      requestId,
    };
  },
  addHeaders: {
    'x-ratelimit-limit': true,
    'x-ratelimit-remaining': true,
    'x-ratelimit-reset': true,
  },
};

/**
 * Health check middleware for load balancers
 */
export const healthCheckMiddleware = async (_request: FastifyRequest, reply: FastifyReply) => {
  // Simple health check - just return 200 OK
  return reply.status(200).send({ status: 'ok', timestamp: new Date().toISOString() });
};

/**
 * Request size limit middleware
 */
export const requestSizeLimitMiddleware = {
  bodyLimit: 10 * 1024 * 1024, // 10MB limit
};

/**
 * Compression middleware configuration
 */
export const compressionMiddleware = {
  global: true,
  threshold: 1024, // Only compress responses larger than 1KB
  encodings: ['gzip', 'deflate'],
};

/**
 * Register all middleware with Fastify instance
 */
export const registerMiddleware = async (fastify: FastifyInstance) => {
  // Request ID middleware (first)
  fastify.addHook('preHandler', requestIdMiddleware);
  
  // Request logging middleware
  fastify.addHook('preHandler', requestLoggingMiddleware);
  
  // Error handler
  fastify.setErrorHandler(errorHandlerMiddleware);
  
  // Simple ping route (before rate limiting)
  fastify.get('/ping', healthCheckMiddleware);
  
  logger.info('All middleware registered successfully');
};

export default {
  requestIdMiddleware,
  requestLoggingMiddleware,
  errorHandlerMiddleware,
  corsMiddleware,
  securityMiddleware,
  rateLimitMiddleware,
  healthCheckMiddleware,
  requestSizeLimitMiddleware,
  compressionMiddleware,
  registerMiddleware,
};
