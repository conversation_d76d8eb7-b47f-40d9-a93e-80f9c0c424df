import Fastify, { FastifyInstance } from 'fastify';
import { FastifySSEPlugin } from 'fastify-sse-v2';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import rateLimit from '@fastify/rate-limit';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import underPressure from '@fastify/under-pressure';

import { serviceConfig, ENV } from '@/config';
import { logger } from '@/utils/logger';
import { registerMiddleware, rateLimitMiddleware } from '@/middleware';
import { registerRoutes } from '@/controllers/routes';

/**
 * Build Fastify application with all plugins and routes
 */
export const buildApp = async (): Promise<FastifyInstance> => {
  // Create Fastify instance
  const app = Fastify({
    logger: false, // We use our custom logger
    trustProxy: true,
    bodyLimit: 10 * 1024 * 1024, // 10MB
    keepAliveTimeout: 30000,
    connectionTimeout: 30000,
  });

  try {
    // Register service configuration
    app.decorate('config', serviceConfig);

    // Register SSE plugin first
    await app.register(FastifySSEPlugin, {
      retryDelay: 3000,
    });

    // Register CORS
    await app.register(cors, {
      origin: true,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'X-Request-ID',
        'Accept',
        'Origin',
      ],
      exposedHeaders: ['X-Request-ID'],
    });

    // Register security middleware (Helmet)
    if (serviceConfig.security.helmetEnabled) {
      await app.register(helmet, {
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
          },
        },
        crossOriginEmbedderPolicy: false,
        crossOriginOpenerPolicy: false,
        crossOriginResourcePolicy: false,
        frameguard: { action: 'deny' },
        hidePoweredBy: true,
        hsts: {
          maxAge: 31536000,
          includeSubDomains: true,
          preload: true,
        },
        ieNoOpen: true,
        noSniff: true,
        originAgentCluster: true,
        permittedCrossDomainPolicies: false,
        referrerPolicy: { policy: 'no-referrer' },
        xssFilter: true,
      });
    }

    // Register rate limiting
    await app.register(rateLimit, {
      ...rateLimitMiddleware,
      max: serviceConfig.rateLimit.max,
      timeWindow: serviceConfig.rateLimit.timeWindow,
    });

    // Register under pressure (load monitoring)
    await app.register(underPressure, {
      maxEventLoopDelay: 1000,
      maxHeapUsedBytes: **********, // 1GB
      maxRssBytes: **********, // 1GB
      maxEventLoopUtilization: 0.98,
      message: 'Service under pressure',
      retryAfter: 50,
      healthCheck: async () => {
        // Custom health check logic
        return true;
      },
      healthCheckInterval: 5000,
    });

    // Register Swagger documentation
    await app.register(swagger, {
      openapi: {
        openapi: '3.0.0',
        info: {
          title: 'AI Proxy Service API',
          description: 'High-performance AI proxy service with dual-mode responses and multi-provider support',
          version: process.env['npm_package_version'] || '1.0.0',
          contact: {
            name: 'AI Proxy Service Team',
            email: '<EMAIL>',
          },
          license: {
            name: 'MIT',
            url: 'https://opensource.org/licenses/MIT',
          },
        },
        servers: [
          {
            url: `http://localhost:${serviceConfig.server.port}`,
            description: 'Development server',
          },
        ],
        tags: [
          {
            name: 'Chat',
            description: 'Chat completion endpoints',
          },
          {
            name: 'Models',
            description: 'Model management endpoints',
          },
          {
            name: 'Health',
            description: 'Health check endpoints',
          },
          {
            name: 'Monitoring',
            description: 'Monitoring and metrics endpoints',
          },
          {
            name: 'General',
            description: 'General API endpoints',
          },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
            apiKey: {
              type: 'apiKey',
              in: 'header',
              name: 'X-API-Key',
            },
          },
        },
        security: [
          {
            bearerAuth: [],
          },
          {
            apiKey: [],
          },
        ],
      },
    });

    // Register Swagger UI
    await app.register(swaggerUi, {
      routePrefix: '/docs',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: false,
      },
      staticCSP: true,
      transformStaticCSP: (header) => header,
      transformSpecification: (swaggerObject) => {
        return swaggerObject;
      },
      transformSpecificationClone: true,
    });

    // Register custom middleware
    await registerMiddleware(app);

    // Register all routes
    await registerRoutes(app);

    // Global error handler for uncaught errors
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', { error });
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection', { reason, promise });
      process.exit(1);
    });

    // Graceful shutdown handling
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}, starting graceful shutdown`);
      
      try {
        await app.close();
        logger.info('Fastify server closed successfully');
        process.exit(0);
      } catch (error) {
        logger.error('Error during graceful shutdown', { error });
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    logger.info('Fastify application built successfully', {
      environment: ENV.NODE_ENV,
      port: serviceConfig.server.port,
      host: serviceConfig.server.host,
      logLevel: serviceConfig.server.logLevel,
    });

    return app;
  } catch (error) {
    logger.error('Failed to build Fastify application', { error });
    throw error;
  }
};

/**
 * Start the server
 */
export const startServer = async (): Promise<void> => {
  try {
    const app = await buildApp();
    
    const address = await app.listen({
      port: serviceConfig.server.port,
      host: serviceConfig.server.host,
    });

    logger.info('Server started successfully', {
      address,
      environment: ENV.NODE_ENV,
      pid: process.pid,
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
    });

    // Log available routes
    logger.info('Available routes:', {
      routes: app.printRoutes({ commonPrefix: false }),
    });

    // Log service configuration summary
    logger.info('Service configuration summary', {
      providers: Object.keys(serviceConfig.providers),
      modelsCount: serviceConfig.models.length,
      enabledModels: serviceConfig.models.filter(m => m.enabled).length,
      rateLimit: serviceConfig.rateLimit,
      security: serviceConfig.security,
      monitoring: serviceConfig.monitoring,
    });

  } catch (error) {
    logger.error('Failed to start server', { error });
    process.exit(1);
  }
};

// Start server if this file is run directly
if (require.main === module) {
  startServer();
}

export default { buildApp, startServer };
