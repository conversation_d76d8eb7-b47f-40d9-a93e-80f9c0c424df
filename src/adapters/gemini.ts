import { GoogleGenAI } from '@google/genai';
import {
  AIProviderAdapter,
  ChatCompletionRequest,
  ChatCompletionResponse,
  StreamChunk,
  ProviderError,
  ValidationError,
} from '@/types';
import { serviceConfig } from '@/config';
import { logger, logProviderCall } from '@/utils/logger';
import {
  generateRequestId,
  getCurrentTimestamp,
  getPerformanceTimestamp,
  calculateDuration,
  retryWithBackoff,
  withTimeout,
  validateChatCompletionRequest,
} from '@/utils';

export class GeminiAdapter implements AIProviderAdapter {
  public readonly provider = 'gemini' as const;
  private client!: GoogleGenAI;
  private isInitialized = false;

  constructor() {
    this.initializeClient();
  }

  private initializeClient(): void {
    try {
      const config = serviceConfig.providers.gemini;
      
      // Initialize based on auth method
      if (config.authMethod === 'api-key') {
        this.client = new GoogleGenAI({
          apiKey: config.apiKey,
        });
      } else if (config.authMethod === 'google-auth' && config.project) {
        this.client = new GoogleGenAI({
          vertexai: true,
          project: config.project,
          location: config.location || 'us-central1',
        });
      } else {
        throw new Error('Invalid Gemini configuration');
      }
      
      this.isInitialized = true;
      logger.info('Gemini adapter initialized successfully', {
        authMethod: config.authMethod,
        project: config.project,
        location: config.location,
      });
    } catch (error) {
      logger.error('Failed to initialize Gemini adapter', { error });
      throw new ProviderError('Failed to initialize Gemini adapter', 'gemini', error as Error);
    }
  }

  async isHealthy(): Promise<boolean> {
    if (!this.isInitialized) {
      return false;
    }

    try {
      const startTime = getPerformanceTimestamp();
      
      // Simple health check using a basic generation request
      const response = await withTimeout(
        this.client.models.generateContent({
          model: 'gemini-1.5-flash',
          contents: 'Hello',
          config: {
            maxOutputTokens: 1,
          },
        }),
        5000,
        'Gemini health check timeout'
      );
      
      const duration = calculateDuration(startTime);
      
      logProviderCall(
        this.provider,
        'health-check',
        generateRequestId(),
        'health-check',
        duration,
        true
      );
      
      return !!response;
    } catch (error) {
      logger.warn('Gemini health check failed', { error });
      return false;
    }
  }

  validateRequest(request: ChatCompletionRequest): void {
    validateChatCompletionRequest(request);
    
    // Gemini-specific validations
    if (request.presencePenalty !== undefined) {
      throw new ValidationError('Gemini does not support presencePenalty parameter');
    }
    
    if (request.frequencyPenalty !== undefined) {
      throw new ValidationError('Gemini does not support frequencyPenalty parameter');
    }
    
    if (request.tools !== undefined) {
      logger.warn('Gemini tools support is experimental', { model: request.model });
    }
  }

  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const requestId = generateRequestId();
    const startTime = getPerformanceTimestamp();
    
    try {
      this.validateRequest(request);
      
      const geminiRequest = this.transformRequest(request);
      
      const response = await retryWithBackoff(
        () => withTimeout(
          this.client.models.generateContent(geminiRequest),
          serviceConfig.providers.gemini.timeout,
          'Gemini request timeout'
        ),
        {
          retries: serviceConfig.providers.gemini.maxRetries,
          onFailedAttempt: (error, attemptNumber) => {
            logger.warn('Gemini request attempt failed', {
              requestId,
              attemptNumber,
              error: error.message,
              model: request.model,
            });
          },
        }
      );
      
      const duration = calculateDuration(startTime);
      const transformedResponse = this.transformResponse(response, request.model, duration);
      
      logProviderCall(
        this.provider,
        request.model,
        requestId,
        'chat-completion',
        duration,
        true
      );
      
      return transformedResponse;
    } catch (error) {
      const duration = calculateDuration(startTime);
      
      logProviderCall(
        this.provider,
        request.model,
        requestId,
        'chat-completion',
        duration,
        false,
        (error as Error).message
      );
      
      throw this.handleError(error as Error, requestId);
    }
  }

  async* chatCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk> {
    const requestId = generateRequestId();
    const startTime = getPerformanceTimestamp();
    
    try {
      this.validateRequest(request);
      
      const geminiRequest = this.transformRequest(request);
      
      const stream = await retryWithBackoff(
        () => withTimeout(
          this.client.models.generateContentStream(geminiRequest),
          serviceConfig.providers.gemini.timeout,
          'Gemini stream request timeout'
        ),
        {
          retries: serviceConfig.providers.gemini.maxRetries,
          onFailedAttempt: (error, attemptNumber) => {
            logger.warn('Gemini stream request attempt failed', {
              requestId,
              attemptNumber,
              error: error.message,
              model: request.model,
            });
          },
        }
      );
      
      let chunkCount = 0;
      
      for await (const chunk of stream) {
        chunkCount++;
        yield this.transformStreamChunk(chunk, request.model, chunkCount);
      }
      
      const duration = calculateDuration(startTime);
      
      logProviderCall(
        this.provider,
        request.model,
        requestId,
        'chat-completion-stream',
        duration,
        true
      );
      
    } catch (error) {
      const duration = calculateDuration(startTime);
      
      logProviderCall(
        this.provider,
        request.model,
        requestId,
        'chat-completion-stream',
        duration,
        false,
        (error as Error).message
      );
      
      throw this.handleError(error as Error, requestId);
    }
  }

  private transformRequest(request: ChatCompletionRequest): any {
    // Convert messages to Gemini format
    const contents = this.convertMessagesToContents(request.messages);
    
    const geminiRequest: any = {
      model: request.model,
      contents,
    };

    // Build generation config
    const config: any = {};
    
    if (request.temperature !== undefined) {
      config.temperature = request.temperature;
    }
    
    if (request.maxTokens !== undefined) {
      config.maxOutputTokens = request.maxTokens;
    }
    
    if (request.topP !== undefined) {
      config.topP = request.topP;
    }
    
    if (request.topK !== undefined) {
      config.topK = request.topK;
    }
    
    if (request.stop !== undefined) {
      config.stopSequences = Array.isArray(request.stop) ? request.stop : [request.stop];
    }
    
    if (Object.keys(config).length > 0) {
      geminiRequest.config = config;
    }

    return geminiRequest;
  }

  private convertMessagesToContents(messages: any[]): any[] {
    const contents: any[] = [];
    let systemInstruction = '';
    
    for (const message of messages) {
      if (message.role === 'system') {
        systemInstruction += message.content + '\n';
        continue;
      }
      
      const role = message.role === 'assistant' ? 'model' : 'user';
      
      contents.push({
        role,
        parts: [{ text: message.content }],
      });
    }
    
    // Add system instruction as the first user message if present
    if (systemInstruction.trim()) {
      contents.unshift({
        role: 'user',
        parts: [{ text: `System: ${systemInstruction.trim()}` }],
      });
    }
    
    return contents;
  }

  private transformResponse(response: any, model: string, processingTime: number): ChatCompletionResponse {
    const candidate = response.candidates?.[0];
    const content = candidate?.content?.parts?.[0]?.text || '';
    
    return {
      id: generateRequestId(),
      object: 'chat.completion',
      created: getCurrentTimestamp(),
      model,
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content,
        },
        finishReason: this.mapFinishReason(candidate?.finishReason),
      }],
      usage: {
        promptTokens: response.usageMetadata?.promptTokenCount || 0,
        completionTokens: response.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: response.usageMetadata?.totalTokenCount || 0,
      },
      provider: this.provider,
      processingTime,
    };
  }

  private transformStreamChunk(chunk: any, model: string, index: number): StreamChunk {
    const candidate = chunk.candidates?.[0];
    const content = candidate?.content?.parts?.[0]?.text || '';
    
    return {
      id: generateRequestId(),
      object: 'chat.completion.chunk',
      created: getCurrentTimestamp(),
      model,
      choices: [{
        index: 0,
        delta: {
          role: index === 1 ? 'assistant' as const : undefined,
          content,
        },
        finishReason: this.mapFinishReason(candidate?.finishReason),
      }],
      provider: this.provider,
    };
  }

  private mapFinishReason(geminiReason: string | undefined): string | null {
    if (!geminiReason) return null;
    
    const reasonMap: Record<string, string> = {
      'STOP': 'stop',
      'MAX_TOKENS': 'length',
      'SAFETY': 'content_filter',
      'RECITATION': 'content_filter',
      'OTHER': 'stop',
    };
    
    return reasonMap[geminiReason] || 'stop';
  }

  private handleError(error: Error, requestId: string): ProviderError {
    logger.error('Gemini adapter error', { error, requestId });
    
    // Handle specific Gemini errors
    if (error.message.includes('timeout')) {
      return new ProviderError('Gemini request timeout', this.provider, error);
    }
    
    if (error.message.includes('quota')) {
      return new ProviderError('Gemini quota exceeded', this.provider, error);
    }
    
    if (error.message.includes('API key')) {
      return new ProviderError('Invalid Gemini API key', this.provider, error);
    }
    
    if (error.message.includes('model')) {
      return new ProviderError('Gemini model not found or not supported', this.provider, error);
    }
    
    return new ProviderError(`Gemini error: ${error.message}`, this.provider, error);
  }
}
