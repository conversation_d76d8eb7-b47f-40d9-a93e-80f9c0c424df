import OpenAI from 'openai';
import {
  AIProviderAdapter,
  ChatCompletionRequest,
  ChatCompletionResponse,
  StreamChunk,
  ProviderError,
  ValidationError,
} from '@/types';
import { serviceConfig } from '@/config';
import { logger, logProviderCall } from '@/utils/logger';
import {
  generateRequestId,
  getPerformanceTimestamp,
  calculateDuration,
  retryWithBackoff,
  withTimeout,
  validateChatCompletionRequest,
} from '@/utils';

export class OpenAIAdapter implements AIProviderAdapter {
  public readonly provider = 'openai' as const;
  private client!: OpenAI;
  private isInitialized = false;

  constructor() {
    this.initializeClient();
  }

  private initializeClient(): void {
    try {
      const config = serviceConfig.providers.openai;
      
      this.client = new OpenAI({
        apiKey: config.apiKey,
        baseURL: config.baseURL,
        timeout: config.timeout,
        maxRetries: 0, // We handle retries ourselves
      });
      
      this.isInitialized = true;
      logger.info('OpenAI adapter initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize OpenAI adapter', { error });
      throw new ProviderError('Failed to initialize OpenAI adapter', 'openai', error as Error);
    }
  }

  async isHealthy(): Promise<boolean> {
    if (!this.isInitialized) {
      return false;
    }

    try {
      const startTime = getPerformanceTimestamp();
      
      // Simple health check using models list
      await withTimeout(
        this.client.models.list(),
        5000,
        'OpenAI health check timeout'
      );
      
      const duration = calculateDuration(startTime);
      
      logProviderCall(
        this.provider,
        'health-check',
        generateRequestId(),
        'health-check',
        duration,
        true
      );
      
      return true;
    } catch (error) {
      logger.warn('OpenAI health check failed', { error });
      return false;
    }
  }

  validateRequest(request: ChatCompletionRequest): void {
    validateChatCompletionRequest(request);
    
    // OpenAI-specific validations
    if (request.topK !== undefined) {
      throw new ValidationError('OpenAI does not support topK parameter');
    }
  }

  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const requestId = generateRequestId();
    const startTime = getPerformanceTimestamp();
    
    try {
      this.validateRequest(request);
      
      const openaiRequest = this.transformRequest(request);
      
      const response = await retryWithBackoff(
        () => withTimeout(
          this.client.chat.completions.create(openaiRequest),
          serviceConfig.providers.openai.timeout,
          'OpenAI request timeout'
        ),
        {
          retries: serviceConfig.providers.openai.maxRetries,
          onFailedAttempt: (error, attemptNumber) => {
            logger.warn('OpenAI request attempt failed', {
              requestId,
              attemptNumber,
              error: error.message,
              model: request.model,
            });
          },
        }
      );

      const duration = calculateDuration(startTime);
      const transformedResponse = this.transformResponse(response as OpenAI.Chat.ChatCompletion, duration);
      
      logProviderCall(
        this.provider,
        request.model,
        requestId,
        'chat-completion',
        duration,
        true
      );
      
      return transformedResponse;
    } catch (error) {
      const duration = calculateDuration(startTime);
      
      logProviderCall(
        this.provider,
        request.model,
        requestId,
        'chat-completion',
        duration,
        false,
        (error as Error).message
      );
      
      throw this.handleError(error as Error, requestId);
    }
  }

  async* chatCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk> {
    const requestId = generateRequestId();
    const startTime = getPerformanceTimestamp();
    
    try {
      this.validateRequest(request);
      
      const openaiRequest = this.transformRequest(request, true);
      
      const stream = await retryWithBackoff(
        () => withTimeout(
          this.client.chat.completions.create(openaiRequest),
          serviceConfig.providers.openai.timeout,
          'OpenAI stream request timeout'
        ),
        {
          retries: serviceConfig.providers.openai.maxRetries,
          onFailedAttempt: (error, attemptNumber) => {
            logger.warn('OpenAI stream request attempt failed', {
              requestId,
              attemptNumber,
              error: error.message,
              model: request.model,
            });
          },
        }
      );

      let chunkCount = 0;

      for await (const chunk of stream as any) {
        chunkCount++;
        yield this.transformStreamChunk(chunk);
      }
      
      const duration = calculateDuration(startTime);
      
      logProviderCall(
        this.provider,
        request.model,
        requestId,
        'chat-completion-stream',
        duration,
        true
      );
      
    } catch (error) {
      const duration = calculateDuration(startTime);
      
      logProviderCall(
        this.provider,
        request.model,
        requestId,
        'chat-completion-stream',
        duration,
        false,
        (error as Error).message
      );
      
      throw this.handleError(error as Error, requestId);
    }
  }

  private transformRequest(request: ChatCompletionRequest, stream = false): OpenAI.Chat.ChatCompletionCreateParams {
    const openaiRequest: OpenAI.Chat.ChatCompletionCreateParams = {
      model: request.model,
      messages: request.messages.map(msg => ({
        role: msg.role as any,
        content: msg.content,
        ...(msg.name && { name: msg.name }),
        ...(msg.toolCallId && { tool_call_id: msg.toolCallId }),
      })),
      stream: stream as any,
    };

    // Add optional parameters
    if (request.temperature !== undefined) {
      openaiRequest.temperature = request.temperature;
    }
    
    if (request.maxTokens !== undefined) {
      openaiRequest.max_tokens = request.maxTokens;
    }
    
    if (request.topP !== undefined) {
      openaiRequest.top_p = request.topP;
    }
    
    if (request.stop !== undefined) {
      openaiRequest.stop = request.stop;
    }
    
    if (request.presencePenalty !== undefined) {
      openaiRequest.presence_penalty = request.presencePenalty;
    }
    
    if (request.frequencyPenalty !== undefined) {
      openaiRequest.frequency_penalty = request.frequencyPenalty;
    }
    
    if (request.user !== undefined) {
      openaiRequest.user = request.user;
    }
    
    if (request.tools !== undefined) {
      openaiRequest.tools = request.tools;
    }
    
    if (request.toolChoice !== undefined) {
      openaiRequest.tool_choice = request.toolChoice;
    }

    return openaiRequest;
  }

  private transformResponse(response: OpenAI.Chat.ChatCompletion, processingTime: number): ChatCompletionResponse {
    return {
      id: response.id,
      object: response.object,
      created: response.created,
      model: response.model,
      choices: response.choices.map(choice => ({
        index: choice.index,
        message: {
          role: choice.message.role as any,
          content: choice.message.content || '',
          ...(choice.message.tool_calls && choice.message.tool_calls[0]?.id && { toolCallId: choice.message.tool_calls[0].id }),
        },
        finishReason: choice.finish_reason,
      })),
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0,
      },
      provider: this.provider,
      processingTime,
    };
  }

  private transformStreamChunk(chunk: OpenAI.Chat.ChatCompletionChunk): StreamChunk {
    return {
      id: chunk.id,
      object: chunk.object,
      created: chunk.created,
      model: chunk.model,
      choices: chunk.choices.map(choice => ({
        index: choice.index,
        delta: {
          role: choice.delta.role as any,
          content: choice.delta.content || '',
          ...(choice.delta.tool_calls && choice.delta.tool_calls[0]?.id && { toolCallId: choice.delta.tool_calls[0].id }),
        },
        finishReason: choice.finish_reason,
      })),
      provider: this.provider,
    };
  }

  private handleError(error: Error, requestId: string): ProviderError {
    logger.error('OpenAI adapter error', { error, requestId });
    
    // Handle specific OpenAI errors
    if (error.message.includes('timeout')) {
      return new ProviderError('OpenAI request timeout', this.provider, error);
    }
    
    if (error.message.includes('rate limit')) {
      return new ProviderError('OpenAI rate limit exceeded', this.provider, error);
    }
    
    if (error.message.includes('invalid_api_key')) {
      return new ProviderError('Invalid OpenAI API key', this.provider, error);
    }
    
    if (error.message.includes('model_not_found')) {
      return new ProviderError('OpenAI model not found', this.provider, error);
    }
    
    return new ProviderError(`OpenAI error: ${error.message}`, this.provider, error);
  }
}
