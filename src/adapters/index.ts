import { OpenAIAdapter } from './openai';
import { GeminiAdapter } from './gemini';
import {
  <PERSON><PERSON>rovider,
  AIProviderAdapter,
  ChatCompletionRequest,
  ChatCompletionResponse,
  StreamChunk,
  ModelConfig,
  ProviderError,
  ValidationError,
} from '@/types';
import { serviceConfig, ENV } from '@/config';
import { logger } from '@/utils/logger';
import { generateRequestId } from '@/utils';

export class AdapterManager {
  private adapters: Map<AIProvider, AIProviderAdapter> = new Map();
  private modelConfigs: Map<string, ModelConfig> = new Map();
  private healthStatus: Map<AIProvider, boolean> = new Map();
  private lastHealthCheck: Map<AIProvider, number> = new Map();
  private readonly healthCheckInterval = 60000; // 1 minute

  constructor() {
    this.initializeAdapters();
    this.initializeModelConfigs();
    this.startHealthChecks();
  }

  private initializeAdapters(): void {
    try {
      // Initialize OpenAI adapter
      const openaiAdapter = new OpenAIAdapter();
      this.adapters.set('openai', openaiAdapter);
      this.healthStatus.set('openai', false);
      
      // Initialize Gemini adapter
      const geminiAdapter = new GeminiAdapter();
      this.adapters.set('gemini', geminiAdapter);
      this.healthStatus.set('gemini', false);
      
      logger.info('All adapters initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize adapters', { error });
      throw error;
    }
  }

  private initializeModelConfigs(): void {
    for (const model of serviceConfig.models) {
      this.modelConfigs.set(model.id, model);
    }
    
    logger.info('Model configurations loaded', {
      modelCount: this.modelConfigs.size,
      models: Array.from(this.modelConfigs.keys()),
    });
  }

  private startHealthChecks(): void {
    // Initial health check
    this.performHealthChecks();
    
    // Periodic health checks
    setInterval(() => {
      this.performHealthChecks();
    }, this.healthCheckInterval);
  }

  private async performHealthChecks(): Promise<void> {
    const promises = Array.from(this.adapters.entries()).map(async ([provider, adapter]) => {
      try {
        const isHealthy = await adapter.isHealthy();
        this.healthStatus.set(provider, isHealthy);
        this.lastHealthCheck.set(provider, Date.now());
        
        logger.debug('Health check completed', {
          provider,
          healthy: isHealthy,
        });
      } catch (error) {
        this.healthStatus.set(provider, false);
        this.lastHealthCheck.set(provider, Date.now());
        
        logger.warn('Health check failed', {
          provider,
          error: (error as Error).message,
        });
      }
    });
    
    await Promise.allSettled(promises);
  }

  public getModelConfig(modelId: string): ModelConfig | undefined {
    return this.modelConfigs.get(modelId);
  }

  public getAvailableModels(): ModelConfig[] {
    return Array.from(this.modelConfigs.values()).filter(model => model.enabled);
  }

  public getModelsByProvider(provider: AIProvider): ModelConfig[] {
    return this.getAvailableModels().filter(model => model.provider === provider);
  }

  public isProviderHealthy(provider: AIProvider): boolean {
    return this.healthStatus.get(provider) || false;
  }

  public getProviderHealthStatus(): Record<AIProvider, { healthy: boolean; lastCheck: number }> {
    const status: any = {};
    
    for (const provider of this.adapters.keys()) {
      status[provider] = {
        healthy: this.healthStatus.get(provider) || false,
        lastCheck: this.lastHealthCheck.get(provider) || 0,
      };
    }
    
    return status;
  }

  public async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    const modelConfig = this.getModelConfig(request.model);
    if (!modelConfig) {
      throw new ValidationError(`Model '${request.model}' not found`);
    }

    if (!modelConfig.enabled) {
      throw new ValidationError(`Model '${request.model}' is disabled`);
    }

    const adapter = this.getAdapter(modelConfig.provider);
    
    // Try primary model
    try {
      return await adapter.chatCompletion(request);
    } catch (error) {
      // Handle fallback if enabled
      if (ENV.FALLBACK_ENABLED && modelConfig.fallbackModels && modelConfig.fallbackModels.length > 0) {
        logger.warn('Primary model failed, trying fallback', {
          primaryModel: request.model,
          fallbackModels: modelConfig.fallbackModels,
          error: (error as Error).message,
        });
        
        return await this.tryFallbackModels(request, modelConfig.fallbackModels);
      }
      
      throw error;
    }
  }

  public async* chatCompletionStream(request: ChatCompletionRequest): AsyncGenerator<StreamChunk> {
    const modelConfig = this.getModelConfig(request.model);
    if (!modelConfig) {
      throw new ValidationError(`Model '${request.model}' not found`);
    }

    if (!modelConfig.enabled) {
      throw new ValidationError(`Model '${request.model}' is disabled`);
    }

    const adapter = this.getAdapter(modelConfig.provider);
    
    try {
      yield* adapter.chatCompletionStream(request);
    } catch (error) {
      // For streaming, we don't implement fallback as it's more complex
      // and would require buffering the entire response
      logger.error('Streaming request failed', {
        model: request.model,
        provider: modelConfig.provider,
        error: (error as Error).message,
      });
      
      throw error;
    }
  }

  private async tryFallbackModels(
    originalRequest: ChatCompletionRequest,
    fallbackModels: string[]
  ): Promise<ChatCompletionResponse> {
    for (const fallbackModel of fallbackModels) {
      const fallbackConfig = this.getModelConfig(fallbackModel);
      if (!fallbackConfig || !fallbackConfig.enabled) {
        continue;
      }

      const fallbackAdapter = this.getAdapter(fallbackConfig.provider);
      if (!this.isProviderHealthy(fallbackConfig.provider)) {
        continue;
      }

      try {
        const fallbackRequest = { ...originalRequest, model: fallbackModel };
        const response = await fallbackAdapter.chatCompletion(fallbackRequest);
        
        logger.info('Fallback model succeeded', {
          originalModel: originalRequest.model,
          fallbackModel,
          provider: fallbackConfig.provider,
        });
        
        return response;
      } catch (error) {
        logger.warn('Fallback model failed', {
          fallbackModel,
          provider: fallbackConfig.provider,
          error: (error as Error).message,
        });
        
        continue;
      }
    }
    
    throw new ProviderError('All fallback models failed', 'openai');
  }

  private getAdapter(provider: AIProvider): AIProviderAdapter {
    const adapter = this.adapters.get(provider);
    if (!adapter) {
      throw new ValidationError(`Provider '${provider}' not supported`);
    }

    if (!this.isProviderHealthy(provider)) {
      throw new ProviderError(`Provider '${provider}' is currently unhealthy`, provider);
    }

    return adapter;
  }

  public validateRequest(request: ChatCompletionRequest): void {
    const modelConfig = this.getModelConfig(request.model);
    if (!modelConfig) {
      throw new ValidationError(`Model '${request.model}' not found`);
    }

    const adapter = this.adapters.get(modelConfig.provider);
    if (!adapter) {
      throw new ValidationError(`Provider '${modelConfig.provider}' not supported`);
    }

    adapter.validateRequest(request);
  }

  public getHealthStatus() {
    return {
      adapters: Object.fromEntries(this.adapters.keys()),
      health: this.getProviderHealthStatus(),
      models: {
        total: this.modelConfigs.size,
        enabled: this.getAvailableModels().length,
        byProvider: Object.fromEntries(
          Array.from(this.adapters.keys()).map(provider => [
            provider,
            this.getModelsByProvider(provider).length,
          ])
        ),
      },
    };
  }

  public async refreshHealth(): Promise<void> {
    await this.performHealthChecks();
  }

  public getModelInfo(modelId: string) {
    const config = this.getModelConfig(modelId);
    if (!config) {
      return null;
    }

    return {
      ...config,
      providerHealthy: this.isProviderHealthy(config.provider),
      lastHealthCheck: this.lastHealthCheck.get(config.provider),
    };
  }

  public getAllModelsInfo() {
    return this.getAvailableModels().map(config => ({
      ...config,
      providerHealthy: this.isProviderHealthy(config.provider),
      lastHealthCheck: this.lastHealthCheck.get(config.provider),
    }));
  }
}

// Export singleton instance
export const adapterManager = new AdapterManager();

// Export adapters for direct use if needed
export { OpenAIAdapter, GeminiAdapter };
