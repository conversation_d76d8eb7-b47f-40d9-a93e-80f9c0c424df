import { config } from 'dotenv';
import Jo<PERSON> from 'joi';
import { ServiceConfig, ModelConfig, <PERSON>Provider, AuthMethod } from '@/types';

// Load environment variables
config();

// Environment validation schema
const envSchema = Joi.object({
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
  PORT: Joi.number().port().default(3000),
  HOST: Joi.string().default('0.0.0.0'),
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),
  
  // API Keys
  OPENAI_API_KEY: Joi.string().required(),
  GEMINI_API_KEY: Joi.string().required(),
  
  // Google Cloud
  GOOGLE_CLOUD_PROJECT: Joi.string().optional(),
  GOOGLE_CLOUD_LOCATION: Joi.string().default('us-central1'),
  
  // Rate Limiting
  RATE_LIMIT_MAX: Joi.number().positive().default(100),
  RATE_LIMIT_WINDOW: Joi.number().positive().default(60000),
  
  // Security
  CORS_ORIGIN: Joi.string().default('*'),
  HELMET_ENABLED: Joi.boolean().default(true),
  
  // Monitoring
  HEALTH_CHECK_ENABLED: Joi.boolean().default(true),
  METRICS_ENABLED: Joi.boolean().default(true),
  
  // Models
  DEFAULT_MODEL_PROVIDER: Joi.string().valid('openai', 'gemini').default('openai'),
  DEFAULT_OPENAI_MODEL: Joi.string().default('gpt-4o'),
  DEFAULT_GEMINI_MODEL: Joi.string().default('gemini-2.0-flash'),
  
  // Timeouts
  REQUEST_TIMEOUT: Joi.number().positive().default(30000),
  STREAM_TIMEOUT: Joi.number().positive().default(60000),
  
  // Retry
  MAX_RETRIES: Joi.number().min(0).max(10).default(3),
  RETRY_DELAY: Joi.number().positive().default(1000),
  
  // Load Balancing
  ENABLE_LOAD_BALANCING: Joi.boolean().default(true),
  FALLBACK_ENABLED: Joi.boolean().default(true),
}).unknown();

const { error, value: env } = envSchema.validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

// Default model configurations
const defaultModels: ModelConfig[] = [
  // OpenAI Models
  {
    id: 'gpt-4o',
    name: 'GPT-4o',
    provider: 'openai' as AIProvider,
    authMethod: 'bearer' as AuthMethod,
    maxTokens: 4096,
    temperature: 0.7,
    enabled: true,
    fallbackModels: ['gpt-4o-mini', 'gpt-3.5-turbo'],
    rateLimits: {
      requestsPerMinute: 500,
      tokensPerMinute: 150000,
    },
  },
  {
    id: 'gpt-4o-mini',
    name: 'GPT-4o Mini',
    provider: 'openai' as AIProvider,
    authMethod: 'bearer' as AuthMethod,
    maxTokens: 16384,
    temperature: 0.7,
    enabled: true,
    fallbackModels: ['gpt-3.5-turbo'],
    rateLimits: {
      requestsPerMinute: 1000,
      tokensPerMinute: 200000,
    },
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: 'openai' as AIProvider,
    authMethod: 'bearer' as AuthMethod,
    maxTokens: 4096,
    temperature: 0.7,
    enabled: true,
    rateLimits: {
      requestsPerMinute: 3500,
      tokensPerMinute: 90000,
    },
  },
  
  // Gemini Models
  {
    id: 'gemini-2.0-flash',
    name: 'Gemini 2.0 Flash',
    provider: 'gemini' as AIProvider,
    authMethod: 'api-key' as AuthMethod,
    maxTokens: 8192,
    temperature: 0.7,
    enabled: true,
    fallbackModels: ['gemini-1.5-pro', 'gemini-1.5-flash'],
    rateLimits: {
      requestsPerMinute: 1000,
      tokensPerMinute: 1000000,
    },
  },
  {
    id: 'gemini-1.5-pro',
    name: 'Gemini 1.5 Pro',
    provider: 'gemini' as AIProvider,
    authMethod: 'api-key' as AuthMethod,
    maxTokens: 8192,
    temperature: 0.7,
    enabled: true,
    fallbackModels: ['gemini-1.5-flash'],
    rateLimits: {
      requestsPerMinute: 360,
      tokensPerMinute: 1000000,
    },
  },
  {
    id: 'gemini-1.5-flash',
    name: 'Gemini 1.5 Flash',
    provider: 'gemini' as AIProvider,
    authMethod: 'api-key' as AuthMethod,
    maxTokens: 8192,
    temperature: 0.7,
    enabled: true,
    rateLimits: {
      requestsPerMinute: 1000,
      tokensPerMinute: 1000000,
    },
  },
];

// Build service configuration
export const serviceConfig: ServiceConfig = {
  server: {
    port: env.PORT,
    host: env.HOST,
    logLevel: env.LOG_LEVEL,
  },
  providers: {
    openai: {
      apiKey: env.OPENAI_API_KEY,
      timeout: env.REQUEST_TIMEOUT,
      maxRetries: env.MAX_RETRIES,
    },
    gemini: {
      apiKey: env.GEMINI_API_KEY,
      project: env.GOOGLE_CLOUD_PROJECT,
      location: env.GOOGLE_CLOUD_LOCATION,
      authMethod: 'api-key' as AuthMethod,
      timeout: env.REQUEST_TIMEOUT,
      maxRetries: env.MAX_RETRIES,
    },
  },
  models: defaultModels,
  rateLimit: {
    max: env.RATE_LIMIT_MAX,
    timeWindow: env.RATE_LIMIT_WINDOW,
  },
  security: {
    corsOrigin: env.CORS_ORIGIN,
    helmetEnabled: env.HELMET_ENABLED,
  },
  monitoring: {
    healthCheckEnabled: env.HEALTH_CHECK_ENABLED,
    metricsEnabled: env.METRICS_ENABLED,
  },
};

// Export environment variables for direct access
export const ENV = {
  NODE_ENV: env.NODE_ENV,
  PORT: env.PORT,
  HOST: env.HOST,
  LOG_LEVEL: env.LOG_LEVEL,
  REQUEST_TIMEOUT: env.REQUEST_TIMEOUT,
  STREAM_TIMEOUT: env.STREAM_TIMEOUT,
  MAX_RETRIES: env.MAX_RETRIES,
  RETRY_DELAY: env.RETRY_DELAY,
  ENABLE_LOAD_BALANCING: env.ENABLE_LOAD_BALANCING,
  FALLBACK_ENABLED: env.FALLBACK_ENABLED,
} as const;

export default serviceConfig;
