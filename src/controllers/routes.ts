import { FastifyInstance } from 'fastify';
import {
  chatCompletions,
  chatCompletionSchema,
  getModels,
  getModel,
  healthCheck,
  getMetrics,
  refreshHealth,
} from './chat';

/**
 * Register all API routes
 */
export const registerRoutes = async (fastify: FastifyInstance) => {
  // API v1 routes
  await fastify.register(async (fastify) => {
    // Chat completions endpoint (OpenAI compatible)
    fastify.post('/chat/completions', chatCompletions);

    // Models endpoints (OpenAI compatible)
    fastify.get('/models', {
      schema: {
        description: 'List available models',
        tags: ['Models'],
        response: {
          200: {
            type: 'object',
            properties: {
              object: { type: 'string' },
              data: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    object: { type: 'string' },
                    created: { type: 'number' },
                    owned_by: { type: 'string' },
                    provider: { type: 'string' },
                    name: { type: 'string' },
                    enabled: { type: 'boolean' },
                    maxTokens: { type: 'number' },
                    temperature: { type: 'number' },
                    fallbackModels: {
                      type: 'array',
                      items: { type: 'string' },
                    },
                    rateLimits: {
                      type: 'object',
                      properties: {
                        requestsPerMinute: { type: 'number' },
                        tokensPerMinute: { type: 'number' },
                      },
                    },
                    providerHealthy: { type: 'boolean' },
                    lastHealthCheck: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      },
      handler: getModels,
    });

    fastify.get('/models/:model', {
      schema: {
        description: 'Get specific model information',
        tags: ['Models'],
        params: {
          type: 'object',
          properties: {
            model: { type: 'string' },
          },
          required: ['model'],
        },
        response: {
          200: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              object: { type: 'string' },
              created: { type: 'number' },
              owned_by: { type: 'string' },
              provider: { type: 'string' },
              name: { type: 'string' },
              enabled: { type: 'boolean' },
              maxTokens: { type: 'number' },
              temperature: { type: 'number' },
              fallbackModels: {
                type: 'array',
                items: { type: 'string' },
              },
              rateLimits: {
                type: 'object',
                properties: {
                  requestsPerMinute: { type: 'number' },
                  tokensPerMinute: { type: 'number' },
                },
              },
              providerHealthy: { type: 'boolean' },
              lastHealthCheck: { type: 'number' },
            },
          },
          404: {
            type: 'object',
            properties: {
              error: {
                type: 'object',
                properties: {
                  type: { type: 'string' },
                  message: { type: 'string' },
                  code: { type: 'string' },
                },
              },
            },
          },
        },
      },
      handler: getModel,
    });
  }, { prefix: '/v1' });

  // Health and monitoring endpoints
  await fastify.register(async (fastify) => {
    // Health check endpoint
    fastify.get('/health', {
      schema: {
        description: 'Service health check',
        tags: ['Health'],
        response: {
          200: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              timestamp: { type: 'string' },
              version: { type: 'string' },
              uptime: { type: 'number' },
              adapters: { type: 'object' },
              health: { type: 'object' },
              models: { type: 'object' },
            },
          },
          503: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              timestamp: { type: 'string' },
              version: { type: 'string' },
              uptime: { type: 'number' },
              adapters: { type: 'object' },
              health: { type: 'object' },
              models: { type: 'object' },
            },
          },
        },
      },
      handler: healthCheck,
    });

    // Detailed health check
    fastify.get('/health/detailed', {
      schema: {
        description: 'Detailed service health check',
        tags: ['Health'],
      },
      handler: healthCheck,
    });

    // Refresh health status
    fastify.post('/health/refresh', {
      schema: {
        description: 'Refresh provider health status',
        tags: ['Health'],
        response: {
          200: {
            type: 'object',
            properties: {
              message: { type: 'string' },
              timestamp: { type: 'string' },
            },
          },
        },
      },
      handler: refreshHealth,
    });

    // Metrics endpoint
    fastify.get('/metrics', {
      schema: {
        description: 'Service metrics and statistics',
        tags: ['Monitoring'],
        response: {
          200: {
            type: 'object',
            properties: {
              timestamp: { type: 'string' },
              totalRequests: { type: 'number' },
              successfulRequests: { type: 'number' },
              failedRequests: { type: 'number' },
              averageDuration: { type: 'number' },
              totalTokens: { type: 'number' },
              byProvider: { type: 'object' },
              byModel: { type: 'object' },
              recentErrors: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    requestId: { type: 'string' },
                    model: { type: 'string' },
                    provider: { type: 'string' },
                    error: { type: 'string' },
                    timestamp: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      },
      handler: getMetrics,
    });

    // Root endpoint
    fastify.get('/', {
      schema: {
        description: 'API information',
        tags: ['General'],
        response: {
          200: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              version: { type: 'string' },
              description: { type: 'string' },
              endpoints: {
                type: 'object',
                properties: {
                  chat: { type: 'string' },
                  models: { type: 'string' },
                  health: { type: 'string' },
                  metrics: { type: 'string' },
                  docs: { type: 'string' },
                },
              },
            },
          },
        },
      },
      handler: async (_request, reply) => {
        return reply.send({
          name: 'AI Proxy Service',
          version: process.env['npm_package_version'] || '1.0.0',
          description: 'High-performance AI proxy service with dual-mode responses and multi-provider support',
          endpoints: {
            chat: '/v1/chat/completions',
            models: '/v1/models',
            health: '/health',
            metrics: '/metrics',
            docs: '/docs',
          },
        });
      },
    });
  });

  // Compatibility routes (alternative paths)
  await fastify.register(async (fastify) => {
    // Alternative health check paths
    fastify.get('/ping', healthCheck);
    fastify.get('/status', healthCheck);
    
    // Alternative metrics path
    fastify.get('/stats', getMetrics);
  });
};
