import {
  ChatCompletionRequest,
  ChatCompletionResponse,
  StreamChunk,
  RequestMetrics,
  ValidationError,
} from '@/types';
import { adapterManager } from '@/adapters';
import { logger, logRequest, logResponse, logError } from '@/utils/logger';
import {
  generateRequestId,
  getPerformanceTimestamp,
  calculateDuration,
  getCurrentTimestamp,
} from '@/utils';

export class AIService {
  private metrics: Map<string, RequestMetrics> = new Map();
  private readonly maxMetricsHistory = 1000;

  /**
   * Process chat completion request
   */
  async chatCompletion(
    request: ChatCompletionRequest,
    requestId?: string
  ): Promise<ChatCompletionResponse> {
    const reqId = requestId || generateRequestId();
    const startTime = getPerformanceTimestamp();
    
    try {
      // Validate request
      adapterManager.validateRequest(request);
      
      // Log request
      logRequest(reqId, 'POST', '/v1/chat/completions');
      
      // Get model configuration
      const modelConfig = adapterManager.getModelConfig(request.model);
      if (!modelConfig) {
        throw new ValidationError(`Model '${request.model}' not found`);
      }
      
      logger.info('Processing chat completion request', {
        requestId: reqId,
        model: request.model,
        provider: modelConfig.provider,
        messageCount: request.messages.length,
        stream: false,
      });
      
      // Process request through adapter manager
      const response = await adapterManager.chatCompletion(request);
      
      const duration = calculateDuration(startTime);
      
      // Record metrics
      this.recordMetrics({
        requestId: reqId,
        provider: response.provider,
        model: response.model,
        startTime: startTime,
        endTime: getPerformanceTimestamp(),
        duration,
        tokenUsage: {
          prompt: response.usage.promptTokens,
          completion: response.usage.completionTokens,
          total: response.usage.totalTokens,
        },
        success: true,
      });
      
      // Log response
      logResponse(reqId, 200, duration, response.provider, response.model);
      
      logger.info('Chat completion request completed successfully', {
        requestId: reqId,
        model: response.model,
        provider: response.provider,
        duration,
        tokenUsage: response.usage,
      });
      
      return response;
    } catch (error) {
      const duration = calculateDuration(startTime);
      
      // Record failed metrics
      this.recordMetrics({
        requestId: reqId,
        provider: request.model ? adapterManager.getModelConfig(request.model)?.provider || 'unknown' : 'unknown',
        model: request.model || 'unknown',
        startTime: startTime,
        endTime: getPerformanceTimestamp(),
        duration,
        success: false,
        error: (error as Error).message,
      });
      
      // Log error
      logError(error as Error, reqId, undefined, request.model);
      logResponse(reqId, (error as any).statusCode || 500, duration);
      
      throw error;
    }
  }

  /**
   * Process streaming chat completion request
   */
  async* chatCompletionStream(
    request: ChatCompletionRequest,
    requestId?: string
  ): AsyncGenerator<StreamChunk> {
    const reqId = requestId || generateRequestId();
    const startTime = getPerformanceTimestamp();
    let chunkCount = 0;
    let totalTokens = 0;
    
    try {
      // Validate request
      adapterManager.validateRequest(request);
      
      // Log request
      logRequest(reqId, 'POST', '/v1/chat/completions');
      
      // Get model configuration
      const modelConfig = adapterManager.getModelConfig(request.model);
      if (!modelConfig) {
        throw new ValidationError(`Model '${request.model}' not found`);
      }
      
      logger.info('Processing streaming chat completion request', {
        requestId: reqId,
        model: request.model,
        provider: modelConfig.provider,
        messageCount: request.messages.length,
        stream: true,
      });
      
      // Process request through adapter manager
      const stream = adapterManager.chatCompletionStream(request);
      
      for await (const chunk of stream) {
        chunkCount++;
        
        // Track token usage if available (usually in the last chunk)
        if (chunk.choices[0]?.finishReason) {
          // This is typically the last chunk, estimate tokens
          totalTokens = this.estimateTokens(request.messages, chunk);
        }
        
        yield chunk;
      }
      
      const duration = calculateDuration(startTime);
      
      // Record metrics
      this.recordMetrics({
        requestId: reqId,
        provider: modelConfig.provider,
        model: request.model,
        startTime: startTime,
        endTime: getPerformanceTimestamp(),
        duration,
        tokenUsage: totalTokens > 0 ? {
          prompt: Math.floor(totalTokens * 0.7), // Estimate
          completion: Math.floor(totalTokens * 0.3), // Estimate
          total: totalTokens,
        } : undefined,
        success: true,
      });
      
      // Log response
      logResponse(reqId, 200, duration, modelConfig.provider, request.model);
      
      logger.info('Streaming chat completion request completed successfully', {
        requestId: reqId,
        model: request.model,
        provider: modelConfig.provider,
        duration,
        chunkCount,
        estimatedTokens: totalTokens,
      });
      
    } catch (error) {
      const duration = calculateDuration(startTime);
      
      // Record failed metrics
      this.recordMetrics({
        requestId: reqId,
        provider: request.model ? adapterManager.getModelConfig(request.model)?.provider || 'unknown' : 'unknown',
        model: request.model || 'unknown',
        startTime: startTime,
        endTime: getPerformanceTimestamp(),
        duration,
        success: false,
        error: (error as Error).message,
      });
      
      // Log error
      logError(error as Error, reqId, undefined, request.model);
      logResponse(reqId, (error as any).statusCode || 500, duration);
      
      throw error;
    }
  }

  /**
   * Get available models
   */
  getAvailableModels() {
    return adapterManager.getAllModelsInfo();
  }

  /**
   * Get model information
   */
  getModelInfo(modelId: string) {
    return adapterManager.getModelInfo(modelId);
  }

  /**
   * Get service health status
   */
  getHealthStatus() {
    return adapterManager.getHealthStatus();
  }

  /**
   * Refresh provider health status
   */
  async refreshHealth() {
    await adapterManager.refreshHealth();
  }

  /**
   * Get request metrics
   */
  getMetrics() {
    const metrics = Array.from(this.metrics.values());
    
    const summary = {
      totalRequests: metrics.length,
      successfulRequests: metrics.filter(m => m.success).length,
      failedRequests: metrics.filter(m => !m.success).length,
      averageDuration: metrics.length > 0 
        ? metrics.reduce((sum, m) => sum + (m.duration || 0), 0) / metrics.length 
        : 0,
      totalTokens: metrics.reduce((sum, m) => sum + (m.tokenUsage?.total || 0), 0),
      byProvider: this.getMetricsByProvider(metrics),
      byModel: this.getMetricsByModel(metrics),
      recentErrors: metrics
        .filter(m => !m.success && m.error)
        .slice(-10)
        .map(m => ({
          requestId: m.requestId,
          model: m.model,
          provider: m.provider,
          error: m.error,
          timestamp: m.startTime,
        })),
    };
    
    return summary;
  }

  /**
   * Clear metrics history
   */
  clearMetrics() {
    this.metrics.clear();
    logger.info('Metrics history cleared');
  }

  private recordMetrics(metrics: RequestMetrics) {
    this.metrics.set(metrics.requestId, metrics);
    
    // Cleanup old metrics if we exceed the limit
    if (this.metrics.size > this.maxMetricsHistory) {
      const oldestKey = this.metrics.keys().next().value;
      this.metrics.delete(oldestKey);
    }
  }

  private estimateTokens(messages: any[], chunk: StreamChunk): number {
    // Simple token estimation based on character count
    // This is a rough estimate, real token counting would require the actual tokenizer
    const totalChars = messages.reduce((sum, msg) => sum + msg.content.length, 0);
    return Math.ceil(totalChars / 4); // Rough estimate: 1 token ≈ 4 characters
  }

  private getMetricsByProvider(metrics: RequestMetrics[]) {
    const byProvider: Record<string, any> = {};
    
    for (const metric of metrics) {
      if (!byProvider[metric.provider]) {
        byProvider[metric.provider] = {
          requests: 0,
          successful: 0,
          failed: 0,
          totalDuration: 0,
          totalTokens: 0,
        };
      }
      
      const providerMetrics = byProvider[metric.provider];
      providerMetrics.requests++;
      
      if (metric.success) {
        providerMetrics.successful++;
      } else {
        providerMetrics.failed++;
      }
      
      providerMetrics.totalDuration += metric.duration || 0;
      providerMetrics.totalTokens += metric.tokenUsage?.total || 0;
    }
    
    // Calculate averages
    for (const provider in byProvider) {
      const metrics = byProvider[provider];
      metrics.averageDuration = metrics.requests > 0 ? metrics.totalDuration / metrics.requests : 0;
      metrics.successRate = metrics.requests > 0 ? metrics.successful / metrics.requests : 0;
    }
    
    return byProvider;
  }

  private getMetricsByModel(metrics: RequestMetrics[]) {
    const byModel: Record<string, any> = {};
    
    for (const metric of metrics) {
      if (!byModel[metric.model]) {
        byModel[metric.model] = {
          requests: 0,
          successful: 0,
          failed: 0,
          totalDuration: 0,
          totalTokens: 0,
        };
      }
      
      const modelMetrics = byModel[metric.model];
      modelMetrics.requests++;
      
      if (metric.success) {
        modelMetrics.successful++;
      } else {
        modelMetrics.failed++;
      }
      
      modelMetrics.totalDuration += metric.duration || 0;
      modelMetrics.totalTokens += metric.tokenUsage?.total || 0;
    }
    
    // Calculate averages
    for (const model in byModel) {
      const metrics = byModel[model];
      metrics.averageDuration = metrics.requests > 0 ? metrics.totalDuration / metrics.requests : 0;
      metrics.successRate = metrics.requests > 0 ? metrics.successful / metrics.requests : 0;
    }
    
    return byModel;
  }
}

// Export singleton instance
export const aiService = new AIService();
